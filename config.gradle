ext {
    version = [compileSdkVersion: 31,
               buildToolsVersion: '31.0.0',
               minSdkVersion    : 23,
               targetSdkVersion : 31,
               //版本号使用上线时间 规则 年-月-日-次
               versionCode      : 2025072101,
               versionName      : '1.5.5',
    ]
    dependencies = [
            support_v4            : 'androidx.legacy:legacy-support-v4:1.0.0',
            appcompat_v7          : 'androidx.appcompat:appcompat:1.2.0',
            recyclerview_v7       : 'androidx.recyclerview:recyclerview:1.2.1',
            design                : 'com.google.android.material:material:1.4.0',
            cardview_v7           : 'androidx.cardview:cardview:1.0.0',
            annotations           : 'androidx.annotation:annotation:1.0.0',
            constraint_layout     : 'androidx.constraintlayout:constraintlayout:2.1.4',
            multidex              : 'androidx.multidex:multidex:2.0.0',
            pickerview            : 'com.contrarywind:Android-PickerView:4.1.9',
            banner                : 'io.github.youth5201314:banner:2.2.2',
            videocache            : 'com.danikula:videocache:2.7.1',
            swipelayout_library   : 'com.daimajia.swipelayout:library:1.2.0@aar',
            crashreport_upgrade   : 'com.tencent.bugly:crashreport:*******',
            nativecrashreport     : 'com.tencent.bugly:nativecrashreport:3.7.1',
            logger                : 'com.orhanobut:logger:2.2.0',
            avi                   : 'com.wang.avi:library:2.1.3',
            Luban                 : 'top.zibin:Luban:1.1.3',
            rxandroid             : 'io.reactivex.rxjava2:rxandroid:2.0.1',
            retrofit              : 'com.squareup.retrofit2:retrofit:2.9.0',
            converter_gson        : 'com.squareup.retrofit2:converter-gson:2.9.0',
            adapter_rxjava2       : 'com.squareup.retrofit2:adapter-rxjava2:2.9.0',
            rxbinding             : 'com.jakewharton.rxbinding2:rxbinding:2.1.1',
            rxlifecycle_android   : 'com.trello.rxlifecycle2:rxlifecycle-android:2.2.2',
            rxlifecycle_components: 'com.trello.rxlifecycle2:rxlifecycle-components:2.2.2',
            okhttp3               : 'com.squareup.okhttp3:okhttp:3.9.1',
            logging_interceptor   : 'com.squareup.okhttp3:logging-interceptor:3.4.1',
            SuperTextView         : 'com.github.chenBingX:SuperTextView:v3.2.6.64',
            glide_transformations : 'jp.wasabeef:glide-transformations:4.1.0',
            FlycoTabLayout_Lib    : 'com.flyco.tablayout:FlycoTabLayout_Lib:2.1.2@aar',
            kongzue_dialog        : 'com.kongzue.dialog:dialog:2.4.8',
            paperdb               : 'io.paperdb:paperdb:2.7.1',
    ]
}