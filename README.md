# Sleep_CN 睡眠健康应用 - 项目交接文档

## 📋 项目概述

**项目名称**: Sleep_CN (睡眠健康)
**项目类型**: Android原生应用
**版本**: 1.5.4 (2025071401)
**支持平台**: Android
**主要功能**: 睡眠监测、睡眠辅助、睡眠报告、白噪音播放、心率监测、呼吸练习、睡眠习惯培养

## 🏗️ 技术架构

### 核心技术栈
- **开发语言**: Kotlin + Java
- **架构模式**: MVVM
- **数据绑定**: DataBinding + ViewBinding
- **状态管理**: LiveData + ViewModel
- **网络请求**: Retrofit2 + OkHttp3 + RxJava2
- **音频播放**: 自定义Player模块
- **本地存储**: Room + MMKV + PaperDB
- **图片加载**: Glide
- **UI组件**: SuperTextView + RecyclerView + ConstraintLayout
- **动画效果**: Lottie + PAG动画

### 项目架构模式
采用MVVM架构：
- **Model**: 数据模型 (`com.imoblife.goodsleep.bean`)
- **View**: UI界面 (`com.imoblife.goodsleep.activity`, `com.imoblife.goodsleep.fragment`)
- **ViewModel**: 业务逻辑 (`com.imoblife.goodsleep.viewmodel`)
- **Repository**: 数据仓库 (`com.imoblife.goodsleep.repository`)

## 📁 项目结构

```
app/src/main/java/com/imoblife/goodsleep/
├── activity/                  # 活动页面
│   ├── main/                 # 主页面
│   ├── monitor/              # 监测相关
│   ├── sleep/                # 睡眠相关
│   ├── heartrate/            # 心率监测
│   ├── breathingexercises/   # 呼吸练习
│   ├── user/                 # 用户相关
│   └── ...
├── adapter/                   # 适配器
├── bean/                      # 数据模型
├── constant/                  # 常量定义
├── databinding/               # 数据绑定
├── db/                        # 数据库
├── ext/                       # Kotlin扩展
├── fragment/                  # 片段
│   ├── home/                 # 首页
│   ├── report/               # 报告
│   ├── sleep/                # 睡眠
│   ├── sleepaid/             # 睡眠辅助
│   └── user/                 # 用户
├── model/                     # 数据管理
├── mvvm/                      # MVVM基础类
├── net/                       # 网络请求
├── player/                    # 播放器
├── repository/                # 数据仓库
├── service/                   # 服务
├── sleep/                     # 睡眠监测核心
├── statistics/                # 统计分析
├── util/                      # 工具类
├── view/                      # 自定义视图
├── viewmodel/                 # 视图模型
├── MyApplication.kt           # 应用入口
└── ...
```

## 🚀 快速开始

### 环境要求
- Android Studio Arctic Fox (2020.3.1) 或更高版本
- JDK 11
- Kotlin 1.6.21
- Gradle 7.3.1
- minSdkVersion: 23
- targetSdkVersion: 31

### 构建步骤
1. **克隆项目**
   ```bash
   git clone [项目地址]
   cd Sleep_CN
   ```

2. **配置签名**
   - 签名文件: `app/88326590.keystore`
   - 密码: `88326590`
   - 别名: `imoblife_android_keystore`

3. **构建项目**
   ```bash
   # 调试版本
   ./gradlew assembleDebug
   
   # 发布版本
   ./gradlew assembleRelease
   
   # 渠道包
   ./gradlew assembleReleaseChannels
   ```

4. **运行项目**
   - 通过Android Studio直接运行
   - 或使用命令: `./gradlew installDebug`

## 🔧 核心功能模块详解

### 1. 睡眠监测 (monitor)
**位置**: `com.imoblife.goodsleep.activity.monitor.sleep`

**主要功能**:
- 睡眠状态监测
- 音频记录与分析
- 睡眠报告生成
- 睡眠质量评估

**关键文件**:
- `SleepMonitorActivity.kt`: 睡眠监测主界面
- `SleepMonitorViewModel.kt`: 睡眠监测业务逻辑
- `SleepMonitorCenter.kt`: 睡眠监测核心管理

**代码示例**:
```kotlin
// 开始睡眠监测
private fun startMonitoring() {
    SleepMonitorCenter.getInstance().startMonitor(object : SleepMonitorCenter.SleepMonitorListener {
        override fun sleepDbChange(db: Int) {
            // 处理音量变化
        }
        
        override fun sleepMonitorRunning(isRunning: Boolean) {
            // 处理监测状态
        }
    })
}
```

### 2. 睡眠辅助 (sleepaid)
**位置**: `com.imoblife.goodsleep.fragment.sleepaid`

**主要功能**:
- 白噪音播放
- 睡眠音乐
- 睡眠引导课程
- 收藏管理

**关键文件**:
- `SleepAidNewFragment.kt`: 睡眠辅助主界面
- `SleepAidViewModel.kt`: 睡眠辅助业务逻辑
- `SleepAidRepository.kt`: 睡眠辅助数据仓库

### 3. 心率监测 (heartrate)
**位置**: `com.imoblife.goodsleep.activity.heartrate`

**主要功能**:
- 心率测量
- 心率历史记录
- 心率报告分析

**关键文件**:
- `HeartRateMonitoringActivity.kt`: 心率监测主界面
- `HeartRateMonitoringViewModel.kt`: 心率监测业务逻辑

### 4. 呼吸练习 (breathingexercises)
**位置**: `com.imoblife.goodsleep.activity.breathingexercises`

**主要功能**:
- 呼吸引导
- 呼吸模式选择
- 呼吸练习记录

**关键文件**:
- `BreathingExercisesActivity.kt`: 呼吸练习主界面
- `BreathingExercisesViewModel.kt`: 呼吸练习业务逻辑

### 5. 睡眠报告 (report)
**位置**: `com.imoblife.goodsleep.fragment.report`

**主要功能**:
- 睡眠数据统计
- 睡眠质量分析
- 睡眠趋势图表
- 睡眠建议

**关键文件**:
- `SleepReportFragment.kt`: 睡眠报告主界面
- `SleepReportViewModel.kt`: 睡眠报告业务逻辑

## 🌐 网络请求

### API配置
- **基础URL**: 
  - 测试环境: `https://sleepmirror.navolove.com/`
  - 生产环境: `https://sleep.navolove.com/`
- **请求封装**: 基于Retrofit2 + OkHttp3 + RxJava2
- **错误处理**: 统一错误拦截和提示
- **缓存策略**: OkHttp缓存拦截器

### 主要API接口
- 用户登录: `ApiUserService.login`
- 睡眠监测: `ApiServiceSleep.uploadSleepMonitor`
- 睡眠报告: `ApiServiceSleep.getSleepReport`
- 白噪音列表: `ApiService.getWhiteNoiseList`
- 心率上传: `ApiServiceSleep.uploadHeartRate`

## 💾 数据存储

### 本地存储
- **Room**: 用户信息、课程数据、睡眠记录
- **MMKV**: 配置项、缓存数据
- **PaperDB**: 非关系型数据存储
- **SharedPreferences**: 简单键值对存储

### 关键数据表
- `User`: 用户信息
- `Course`: 课程信息
- `SleepRecord`: 睡眠记录
- `SleepMonitor`: 睡眠监测数据
- `SleepSoundBite`: 睡眠声音片段

## 🔐 第三方集成

### 1. 微信SDK (ShareSDK)
- **App ID**: wxf5b117d5e8bf1b51
- **功能**: 登录、分享
- **配置**: `app/build.gradle` 中的 MobSDK 配置

### 2. 一键登录 (闪验)
- **SDK版本**: v2.3.7.0
- **功能**: 手机号一键登录
- **位置**: `libs/shanyan_sdk_v2.3.7.0.aar`

### 3. 推送服务 (MobPush)
- **功能**: 消息推送、通知管理
- **配置**: `app/build.gradle` 中的 MobPush 配置

### 4. 数据统计 (神策)
- **功能**: 用户行为统计
- **事件**: 登录、页面曝光、功能点击
- **配置**: `com.sensorsdata.analytics.android:SensorsAnalyticsSDK:6.8.3`

### 5. 支付功能
- **支持**: 支付宝、微信支付
- **管理**: `PayCenter.java`

## 🎯 关键业务流程

### 睡眠监测流程
1. 用户进入睡眠监测页面
2. 设置闹钟和监测参数
3. 开始监测（录音、传感器数据收集）
4. 监测过程中可播放白噪音
5. 闹钟响起或用户手动结束监测
6. 上传监测数据生成睡眠报告
7. 展示睡眠质量分析和建议

### 白噪音播放流程
1. 用户选择白噪音类别
2. 选择具体的白噪音
3. 设置播放参数（音量、定时）
4. 开始播放
5. 后台播放支持
6. 定时关闭或手动停止

### 心率监测流程
1. 用户进入心率监测页面
2. 使用手机摄像头覆盖手指
3. 开始监测心率
4. 数据采集和分析
5. 生成心率报告
6. 保存历史记录

## 🛠️ 开发工具和调试

### 调试工具
- **DoKit**: 开发调试面板
- **Logger**: 日志输出
- **Debug DB**: 数据库调试

### 性能监控
- **神策统计**: 用户行为分析
- **Bugly**: 崩溃监控和上报

## 📱 平台特性

### Android特性
- 渠道包管理 (walle)
- 权限动态申请
- 后台服务保活
- 通知管理
- 深色模式支持

## 🔧 常见问题和解决方案

### 1. 编译问题
- **依赖冲突**: 检查 `build.gradle` 中的强制版本配置
- **版本不兼容**: 确保 Kotlin 和 Gradle 版本匹配
- **缓存问题**: 运行 `./gradlew clean`

### 2. 网络请求问题
- **环境配置**: 检查 `ConsUrl.java` 中的 API 地址
- **证书问题**: 确认HTTPS证书有效性
- **超时问题**: 调整OkHttp的超时配置
- **Token过期**: 检查用户登录状态

### 3. 音频播放问题
- **权限问题**: 确认音频播放权限
- **缓存问题**: 清理音频缓存目录
- **后台播放**: 检查后台播放服务状态
- **耳机适配**: 测试有线/无线耳机兼容性

### 4. 第三方SDK问题
- **微信SDK**: 检查App ID和签名配置
- **一键登录**: 确认运营商网络环境
- **推送服务**: 检查推送配置和证书
- **支付问题**: 验证支付配置和商户信息

## 🛠️ 维护和更新指南

### 定期维护任务
1. **依赖更新**: 每月检查并更新依赖包
2. **安全扫描**: 定期进行安全漏洞扫描
3. **性能监控**: 关注应用性能指标
4. **用户反馈**: 及时处理用户反馈问题

### 版本发布检查清单
- [ ] 代码审查完成
- [ ] 单元测试通过
- [ ] 集成测试通过
- [ ] 性能测试通过
- [ ] 安全检查完成
- [ ] 文档更新完成
- [ ] 版本号更新
- [ ] 发布说明准备
