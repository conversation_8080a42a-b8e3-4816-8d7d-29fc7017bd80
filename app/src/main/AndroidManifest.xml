<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-sdk tools:overrideLibrary="com.hi.dhl.jprogressview" />

    <uses-permission
        android:name="android.permission.READ_PRIVILEGED_PHONE_STATE"
        tools:ignore="ProtectedPermissions" />

    <permission
        android:name="${applicationId}.permission.MIPUSH_RECEIVE"
        android:protectionLevel="signature" />
    <permission
        android:name="${applicationId}.permission.C2D_MESSAGE"
        android:protectionLevel="signature" />

    <queries>
        <package android:name="com.tencent.mm" />
    </queries>

    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.RECEIVE_USER_PRESENT" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="${applicationId}.permission.RECEIVE" />
    <uses-permission android:name="${applicationId}.permission.C2D_MESSAGE" />
    <uses-permission android:name="com.coloros.mcs.permission.RECIEVE_MCS_MESSAGE" />
    <uses-permission android:name="com.xiaomi.sdk.permission.APP" />
    <uses-permission android:name="com.xiaomi.permission.AUTH_SERVICE" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission
        android:name="android.permission.WRITE_CONTACTS"
        tools:node="remove" />
    <uses-permission
        android:name="android.permission.READ_CONTACTS"
        tools:node="remove" />
    <uses-permission
        android:name="android.permission.GET_ACCOUNTS"
        tools:node="remove" />

    <uses-permission android:name="android.permission.FLASHLIGHT" />

    <uses-feature android:name="android.hardware.camera" />
    <uses-feature android:name="android.hardware.camera.flash" />

    <application
        android:name="com.imoblife.goodsleep.MyApplication"
        android:allowBackup="false"
        android:extractNativeLibs="true"
        android:icon="@drawable/icon_logo"
        android:label="@string/app_name"
        android:largeHeap="true"
        android:maxAspectRatio="2.4"
        android:networkSecurityConfig="@xml/network_security_config"
        android:requestLegacyExternalStorage="true"
        android:resizeableActivity="true"
        android:supportsRtl="true"
        android:theme="@style/AppTheme"
        android:useEmbeddedDex="false"
        android:usesCleartextTraffic="true"
        tools:ignore="LockedOrientationActivity"
        tools:node="merge"
        tools:remove="android:appComponentFactory"
        tools:replace="allowBackup"
        tools:targetApi="p">

        <activity
            android:name=".activity.questionnaire.twentyone.ObTwentyOneVpActivity"
            android:exported="false"
            android:screenOrientation="portrait" />

        <activity
            android:name=".activity.privacymanage.DevicePrivacyActivity"
            android:exported="false"
            android:screenOrientation="portrait" />

        <service
            android:name=".util.vibration.VibrationService"
            android:enabled="true"
            android:exported="false" />

        <activity
            android:name=".activity.breathingexercises.BreathingSettingActivity"
            android:exported="false"
            android:screenOrientation="portrait" />

        <activity
            android:name=".activity.breathingexercises.BreathingActivity"
            android:exported="false"
            android:screenOrientation="portrait" />

        <activity
            android:name=".activity.breathingexercises.BreathingListActivity"
            android:exported="false"
            android:screenOrientation="portrait" />

        <activity
            android:name=".activity.redemptioncode.RedemptionCodeActivity"
            android:exported="false"
            android:screenOrientation="portrait" />

        <activity
            android:name=".activity.member.ShareBannerVipSkuSleepReportActivity"
            android:exported="false"
            android:launchMode="singleTop"
            android:theme="@style/AppTheme.Transparent" />

        <activity
            android:name="com.imoblife.goodsleep.activity.member.VipSkuSleepReportActivity"
            android:exported="false"
            android:theme="@style/AppTheme.Transparent" />

        <activity
            android:name=".activity.questionnaire.sixteen.ObSixteenGuideTwoActivity"
            android:exported="false"
            android:screenOrientation="portrait" />

        <activity
            android:name=".activity.questionnaire.sixteen.ObSixteenGuideOneActivity"
            android:exported="false"
            android:screenOrientation="portrait" />

        <activity
            android:name=".activity.questionnaire.sixteen.ObSixteenDetailActivity"
            android:exported="false"
            android:screenOrientation="portrait" />

        <activity
            android:name=".activity.questionnaire.sixteen.ObSixteenStageActivity"
            android:exported="false"
            android:screenOrientation="portrait" />

        <activity
            android:name=".activity.questionnaire.sixteen.ObSixteenGuideActivity"
            android:exported="false"
            android:screenOrientation="portrait" />

        <activity
            android:name=".activity.questionnaire.tip.ObSevenTipPersonalTailorActivity"
            android:exported="false"
            android:screenOrientation="portrait" />

        <activity
            android:name=".activity.play.CourseDetailActivity"
            android:exported="false"
            android:screenOrientation="portrait" />

        <activity
            android:name=".activity.sleep.SleepCeremonyCompleteActivity"
            android:exported="false"
            android:screenOrientation="portrait" />

        <activity
            android:name=".activity.sleep.SleepCeremonyTaskActivity"
            android:exported="false"
            android:screenOrientation="portrait" />

        <activity
            android:name=".activity.sleep.SleepCeremonyMoreActivity"
            android:exported="false"
            android:screenOrientation="portrait" />

        <activity
            android:name=".activity.sleep.SleepCeremonyActivity"
            android:exported="false"
            android:screenOrientation="portrait" />

        <activity
            android:name=".activity.member.VipSleepPlanLockActivity"
            android:exported="false"
            android:screenOrientation="portrait" />

        <activity
            android:name=".activity.questionnaire.ob.ObFifteenPersonalTailorActivity"
            android:exported="false"
            android:screenOrientation="portrait" />

        <activity
            android:name=".activity.questionnaire.ob.ObFifteenVpActivity"
            android:exported="false"
            android:screenOrientation="portrait" />

        <activity
            android:name=".activity.questionnaire.ob.ObFifteenStageActivity"
            android:exported="false"
            android:screenOrientation="portrait" />

        <activity
            android:name=".activity.questionnaire.ob.ObFifteenGuideStageActivity"
            android:exported="false"
            android:screenOrientation="portrait" />

        <activity
            android:name=".activity.questionnaire.ob.ObFifteenGuideActivity"
            android:exported="false"
            android:screenOrientation="portrait" />

        <activity
            android:name=".activity.questionnaire.silent.SilentUsersGuideActivity"
            android:exported="false"
            android:screenOrientation="portrait" />

        <activity
            android:name=".activity.order.OrderDetailActivity"
            android:exported="false"
            android:screenOrientation="portrait" />

        <activity
            android:name=".activity.order.OrderListActivity"
            android:exported="false"
            android:screenOrientation="portrait" />

        <activity
            android:name=".activity.challenge.ChallengeActivity"
            android:exported="false"
            android:screenOrientation="portrait">

            <intent-filter>

                <action android:name="com.yunyang.shortcut.CHALLENGE" />
                <category android:name="android.intent.category.DEFAULT" />

            </intent-filter>

        </activity>

        <activity
            android:name=".activity.notifications.OnSiteNotificationsDetailActivity"
            android:exported="false"
            android:screenOrientation="portrait" />

        <activity
            android:name=".activity.notifications.OnSiteNotificationsActivity"
            android:exported="false"
            android:screenOrientation="portrait" />

        <activity
            android:name=".activity.heartrate.AddHeartRateRemindActivity"
            android:exported="false"
            android:screenOrientation="portrait" />

        <activity
            android:name=".activity.heartrate.HeartRateRemindActivity"
            android:exported="false"
            android:screenOrientation="portrait" />

        <activity
            android:name=".activity.member.ReviewActivity"
            android:exported="false"
            android:theme="@style/AppTheme.Transparent" />
        <activity
            android:name=".activity.heartrate.HeartRateReportFormsActivity"
            android:exported="false"
            android:screenOrientation="portrait" />

        <activity
            android:name=".activity.heartrate.HeartRateResultActivity"
            android:exported="false"
            android:screenOrientation="portrait" />

        <activity
            android:name=".activity.heartrate.HeartRateTransitionActivity"
            android:exported="false"
            android:screenOrientation="portrait" />

        <activity
            android:name=".activity.heartrate.HeartRateMonitoringActivity"
            android:exported="false"
            android:screenOrientation="portrait" />

        <activity
            android:name=".activity.heartrate.HeartRateStartActivity"
            android:exported="false"
            android:screenOrientation="portrait" />

        <activity
            android:name=".activity.questionnaire.transition.TransitionStageElevenActivity"
            android:exported="false"
            android:screenOrientation="portrait" />

        <activity
            android:name=".activity.questionnaire.transition.TransitionQuestionGuideActivity"
            android:exported="false"
            android:screenOrientation="portrait" />

        <activity
            android:name=".activity.questionnaire.transition.TransitionGoodStartActivity"
            android:exported="false"
            android:screenOrientation="portrait" />

        <activity
            android:name=".activity.monitor.SleepMonitorCompleteSkuActivity"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.play.PurchasedCourseActivity"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.play.TrialListeningCourseActivity"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.questionnaire.silent.SilentPersonalTailorActivity"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.questionnaire.silent.SilentDrawUpAPlanActivity"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.questionnaire.silent.SilentUsersStageActivity"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.questionnaire.silent.SilentUsersQuestionnaireStageActivity"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.questionnaire.transition.TransitionPersonalTailorActivity"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.questionnaire.transition.TransitionDrawUpAPlanActivity"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.questionnaire.transition.TransitionStageActivity"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.questionnaire.transition.TransitionQuestionnaireGuideActivity"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.questionnaire.transition.TransitionQuestionnaireStageActivity"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.monitor.state.BedtimeStateActivity"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.sleep.SleepAidActivity"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.mooddiary.MoodDiaryItemActivity"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.mooddiary.MoodDiaryAddActivity"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.breathingexercises.BreathingExercisesActivity"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.mooddiary.MoodDiaryActivity"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.bedtimechecklist.BedtimeChecklistActivity"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.main.MainSleepGuideDialogActivity"
            android:exported="false"
            android:theme="@style/AppTheme.Transparent" />
        <activity
            android:name=".activity.member.VipEquityVideoActivity"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.questionnaire.hint.HintPersonalTailorActivity"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.questionnaire.hint.HintDrawUpAPlanActivity"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.questionnaire.hint.HintGoodStartActivity"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.questionnaire.hint.HintTransformationStageActivity"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.questionnaire.hint.HintStageActivity"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.questionnaire.hint.HintQuestionGuideActivity"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.member.VipSkuBActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|smallestScreenSize|uiMode"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.questionnaire.tip.TipStageActivity"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.questionnaire.tip.TipPersonalTailorActivity"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.questionnaire.tip.TipDrawUpAPlanActivity"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.questionnaire.tip.TipGoodStartActivity"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.questionnaire.tip.TipTransformationStageActivity"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.questionnaire.tip.TipQuestionGuideActivity"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.member.PurchaseMemberDialogActivity"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.questionnaire.exp.EXPQuestionnaireSilentUserActivity"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.ShareActivity"
            android:exported="false"
            android:theme="@style/AppTheme.Transparent" />
        <activity
            android:name=".activity.member.SubscribeCoreActivity"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.member.SubscribeActivity"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.faq.FAQActivity"
            android:exported="true"
            android:screenOrientation="portrait">

            <intent-filter>

                <action android:name="com.yunyang.shortcut.QA" />
                <category android:name="android.intent.category.DEFAULT" />

            </intent-filter>

        </activity>
        <activity
            android:name=".activity.article.SleepArticleActivity"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.dream.SleepDreamTypeActivity"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.dream.SleepDreamAnalysisActivity"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.monitor.SetRingingToneActivity"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.monitor.SleepReminderActivity"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.monitor.sleep.SleepSoundActivity"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.questionnaire.exp.EXPStageActivity"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.play.PlayAudioActivity"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.play.PlayAudioFullActivity"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.play.TimerActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.Transparent" />
        <activity
            android:name=".activity.questionnaire.ObPersonalTailorActivity"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.questionnaire.ObDrawUpAPlanActivity"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.imoblife.goodsleep.activity.area.CountryActivity"
            android:exported="false"
            android:screenOrientation="portrait" />

        <activity
            android:name="com.imoblife.goodsleep.activity.webview.WebViewActivity"
            android:exported="true"
            android:screenOrientation="portrait" />

        <activity
            android:name=".activity.monitor.sleep.SleepGuideActivity"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.monitor.SetSleepTimeDialogActivity"
            android:exported="false"
            android:theme="@style/AppTheme.Transparent" />
        <activity
            android:name=".activity.monitor.history.SleepRecordingActivity"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.monitor.history.AllSleepHistoryActivity"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.monitor.report.SleepMonitoringReportCourseActivity"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.monitor.report.SleepMonitoringReportActivity"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.monitor.ExitSleepAnalysisActivity"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.monitor.alarm.SetSleepAlarmClockActivity"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.monitor.sleep.SleepMonitorActivity"
            android:exported="false"
            android:launchMode="singleTask"
            android:screenOrientation="portrait" />

        <service
            android:name=".sleep.SleepMonitorService"
            android:enabled="true"
            android:exported="true" />
        <service
            android:name=".util.alarmmanager.AlarmService"
            android:enabled="true"
            android:exported="true">
            <intent-filter>
                <action android:name="com.yunyang.alarm.service" />
            </intent-filter>
        </service>

        <activity
            android:name=".activity.collect.CollectActivity"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name=".activity.user.CourseListActivity"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.imoblife.goodsleep.activity.setting.UserInfoActivity"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.imoblife.goodsleep.activity.setting.AboutActivity"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.imoblife.goodsleep.activity.setting.SettingsActivity"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.imoblife.goodsleep.activity.main.MainActivity"
            android:exported="true"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:windowSoftInputMode="stateAlwaysHidden">

            <intent-filter>

                <action android:name="com.yunyang.shortcut.MAIN" />
                <category android:name="android.intent.category.DEFAULT" />

            </intent-filter>

        </activity>
        <activity
            android:name="com.imoblife.goodsleep.activity.user.BindingActivity"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.imoblife.goodsleep.activity.user.PhoneLoginActivity"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.imoblife.goodsleep.activity.user.VerificationCodeActivity"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.imoblife.goodsleep.activity.deeplink.DeepLinkActivity"
            android:exported="true"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:theme="@style/AppFullScreenTheme">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="com.imoblife.goodsleep.deeplink"
                    android:scheme="goodsleep" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.imoblife.goodsleep.activity.member.VipSkuGActivity"
            android:exported="false"
            android:theme="@style/AppTheme.Transparent" />
        <activity
            android:name="com.imoblife.goodsleep.activity.member.VipSkuAActivity"
            android:exported="false"
            android:screenOrientation="portrait" />
        <activity
            android:name="com.imoblife.goodsleep.activity.welcome.WelcomeActivity"
            android:exported="true"
            android:screenOrientation="portrait"
            android:theme="@style/AppFullScreenTheme">
            <meta-data
                android:name="android.app.shortcuts"
                android:resource="@xml/shortcuts" />

            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <action android:name="com.yunyang.shortcut.CHALLENGE" />
                <action android:name="com.yunyang.shortcut.QA" />
                <action android:name="com.yunyang.shortcut.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="now.app"
                    android:scheme="open" />
                <data android:scheme="mtjf42b69a7bb" />
            </intent-filter>

            <!-- 神策数据 Deeplink -->
            <intent-filter>
                <data
                    android:host="sensorsdata"
                    android:pathPrefix="/sd/gZI"
                    android:scheme="now.app" />

                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
            </intent-filter>
            <intent-filter android:autoVerify="true">
                <data
                    android:host="sensor.navoinfo.cn"
                    android:pathPrefix="/sd/gZI"
                    android:scheme="http" />

                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
            </intent-filter>
            <intent-filter android:autoVerify="true">
                <data
                    android:host="sensor.navoinfo.cn"
                    android:pathPrefix="/sd/gZI"
                    android:scheme="https" />

                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
            </intent-filter>
        </activity>
        <activity
            android:name="cn.sharesdk.tencent.qq.ReceiveActivity"
            android:exported="true"
            android:launchMode="singleTask"
            android:noHistory="true">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="tencent1104987898" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.mob.tools.MobUIShell"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:exported="true"
            android:theme="@android:style/Theme.Translucent.NoTitleBar"
            android:windowSoftInputMode="stateHidden|adjustResize">
            <intent-filter>
                <action android:name="com.sina.weibo.sdk.action.ACTION_SDK_REQ_ACTIVITY" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.imoblife.goodsleep.wxapi.WXEntryActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:exported="true"
            android:launchMode="singleTask"
            android:taskAffinity="com.imoblife.goodsleep"
            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
        <activity
            android:name="com.imoblife.goodsleep.wxapi.WXPayEntryActivity"
            android:exported="true"
            android:launchMode="singleTop" />
        <activity
            android:name="com.alipay.sdk.app.AlipayResultActivity"
            android:exported="true"
            tools:node="merge">
            <intent-filter tools:node="replace">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="now_alipay_sdk" />
            </intent-filter>
        </activity>

        <activity
            android:name="com.chuanglan.shanyan_sdk.view.ShanYanOneKeyActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:exported="true"
            android:launchMode="singleTop"
            android:screenOrientation="behind" />
        <activity
            android:name="com.chuanglan.shanyan_sdk.view.CTCCPrivacyProtocolActivity"
            android:configChanges="keyboardHidden|orientation|screenSize"
            android:exported="true"
            android:launchMode="singleTop"
            android:screenOrientation="behind" />
        <activity
            android:name="com.yalantis.ucrop.UCropActivity"
            android:exported="true"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.AppCompat.Light.NoActionBar" />
        <activity
            android:name="com.sensorsdata.analytics.android.sdk.dialog.SchemeActivity"
            android:configChanges="orientation|screenSize"
            android:exported="true"
            android:launchMode="singleTask">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.BROWSABLE" />
                <category android:name="android.intent.category.DEFAULT" />

                <data android:scheme="safdeba76c" />
            </intent-filter>
        </activity>

        <meta-data
            android:name="push_kit_auto_init_enabled"
            android:value="true" />
        <meta-data
            android:name="android.max_aspect"
            android:value="2.4" />
        <meta-data
            android:name="debug_mode"
            android:value="true" />
        <meta-data
            android:name="is_offline_game"
            android:value="true" />
        <meta-data
            android:name="app_key"
            android:value="87RqygW88T4wc888gkW0Kkkc4" />
        <meta-data
            android:name="app_type"
            android:value="1" />
        <meta-data
            android:name="YZ_APP_ID"
            android:value="3943da6739b5e3f015" />
        <meta-data
            android:name="YZ_APP_SECRET"
            android:value="26ca288b6f7d0f3c2171de1e8f771dd0" />
        <meta-data
            android:name="android.notch_support"
            android:value="true" />
        <meta-data
            android:name="notch.config"
            android:value="portrait|landscape" />

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.fileProvider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/provider_paths" />
        </provider> <!-- 网易七鱼SDK频繁自启动 - 禁用相应服务 -->
        <service
            android:name="com.qiyukf.nimlib.service.NimService$Aux"
            android:enabled="false"
            android:exported="true"
            android:process=":core" />

        <uses-library
            android:name="org.apache.http.legacy"
            android:required="false" />

        <provider
            android:name="com.imoblife.goodsleep.Ktx"
            android:authorities="${applicationId}.KtxInstaller"
            android:exported="false" />

        <meta-data
            android:name="com.mob.mobpush.debugLevel"
            android:value="4" />

    </application>

</manifest>