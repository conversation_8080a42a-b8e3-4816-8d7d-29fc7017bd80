<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.appcompat.widget.LinearLayoutCompat
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/shape_common_round_bg"
            android:gravity="center"
            android:orientation="vertical"
            tools:ignore="HardcodedText">

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvCurrentOb"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingLeft="@dimen/qb_px_20"
                android:paddingTop="@dimen/qb_px_20"
                android:paddingRight="@dimen/qb_px_20"
                android:paddingBottom="@dimen/qb_px_10"
                android:text="当前服务器下发问卷为 - "
                android:textColor="@color/main_color" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvOb"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingLeft="@dimen/qb_px_20"
                android:paddingTop="@dimen/qb_px_20"
                android:paddingRight="@dimen/qb_px_20"
                android:paddingBottom="@dimen/qb_px_10"
                android:text="切换ob问卷 =>"
                android:textColor="@color/main_color" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvOne"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:paddingLeft="@dimen/qb_px_20"
                android:paddingTop="@dimen/qb_px_10"
                android:paddingRight="@dimen/qb_px_20"
                android:paddingBottom="@dimen/qb_px_10"
                android:text="1" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvFive"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:paddingLeft="@dimen/qb_px_20"
                android:paddingTop="@dimen/qb_px_10"
                android:paddingRight="@dimen/qb_px_20"
                android:paddingBottom="@dimen/qb_px_10"
                android:text="5" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvSix"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:paddingLeft="@dimen/qb_px_20"
                android:paddingTop="@dimen/qb_px_10"
                android:paddingRight="@dimen/qb_px_20"
                android:paddingBottom="@dimen/qb_px_10"
                android:text="6" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvSeven"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:paddingLeft="@dimen/qb_px_20"
                android:paddingTop="@dimen/qb_px_10"
                android:paddingRight="@dimen/qb_px_20"
                android:paddingBottom="@dimen/qb_px_10"
                android:text="7" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvEight"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:paddingLeft="@dimen/qb_px_20"
                android:paddingTop="@dimen/qb_px_10"
                android:paddingRight="@dimen/qb_px_20"
                android:paddingBottom="@dimen/qb_px_10"
                android:text="8" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvNine"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:paddingLeft="@dimen/qb_px_20"
                android:paddingTop="@dimen/qb_px_10"
                android:paddingRight="@dimen/qb_px_20"
                android:paddingBottom="@dimen/qb_px_10"
                android:text="9" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvTen"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:paddingLeft="@dimen/qb_px_20"
                android:paddingTop="@dimen/qb_px_10"
                android:paddingRight="@dimen/qb_px_20"
                android:paddingBottom="@dimen/qb_px_10"
                android:text="10" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvEleven"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:paddingLeft="@dimen/qb_px_20"
                android:paddingTop="@dimen/qb_px_10"
                android:paddingRight="@dimen/qb_px_20"
                android:paddingBottom="@dimen/qb_px_10"
                android:text="11" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvTwelve"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:paddingLeft="@dimen/qb_px_20"
                android:paddingTop="@dimen/qb_px_10"
                android:paddingRight="@dimen/qb_px_20"
                android:paddingBottom="@dimen/qb_px_10"
                android:text="12" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvThirteen"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:paddingLeft="@dimen/qb_px_20"
                android:paddingTop="@dimen/qb_px_10"
                android:paddingRight="@dimen/qb_px_20"
                android:paddingBottom="@dimen/qb_px_10"
                android:text="13" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvFifteen"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:paddingLeft="@dimen/qb_px_20"
                android:paddingTop="@dimen/qb_px_10"
                android:paddingRight="@dimen/qb_px_20"
                android:paddingBottom="@dimen/qb_px_10"
                android:text="15" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvEighteen"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:paddingLeft="@dimen/qb_px_20"
                android:paddingTop="@dimen/qb_px_10"
                android:paddingRight="@dimen/qb_px_20"
                android:paddingBottom="@dimen/qb_px_10"
                android:text="18" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvTwentyOne"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:paddingLeft="@dimen/qb_px_20"
                android:paddingTop="@dimen/qb_px_10"
                android:paddingRight="@dimen/qb_px_20"
                android:paddingBottom="@dimen/qb_px_10"
                android:text="21" />

            <androidx.appcompat.widget.LinearLayoutCompat
                android:id="@+id/containerOther"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <View
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/qb_px_1"
                    android:background="@color/color_gray" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvChannel"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingLeft="@dimen/qb_px_20"
                    android:paddingTop="@dimen/qb_px_10"
                    android:paddingRight="@dimen/qb_px_20"
                    android:paddingBottom="@dimen/qb_px_10"
                    android:text="其他 =>"
                    android:textColor="@color/main_color" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvSkip"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:paddingLeft="@dimen/qb_px_20"
                    android:paddingTop="@dimen/qb_px_10"
                    android:paddingRight="@dimen/qb_px_20"
                    android:paddingBottom="@dimen/qb_px_10"
                    android:text="跳过" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvCache"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:paddingLeft="@dimen/qb_px_20"
                    android:paddingTop="@dimen/qb_px_10"
                    android:paddingRight="@dimen/qb_px_20"
                    android:paddingBottom="@dimen/qb_px_10"
                    android:text="清除ob标记值「得分」" />

            </androidx.appcompat.widget.LinearLayoutCompat>

        </androidx.appcompat.widget.LinearLayoutCompat>

    </androidx.core.widget.NestedScrollView>

</layout>