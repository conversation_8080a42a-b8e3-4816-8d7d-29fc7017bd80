<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@mipmap/icon_welcome_bg">

        <FrameLayout
            android:id="@+id/fragmentContainer"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvContent"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/qb_px_50"
            android:text="@string/string_target_txt"
            android:textColor="@color/color_white"
            android:textSize="16sp"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/imgBack"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/qb_px_10"
            android:padding="@dimen/qb_px_10"
            android:src="@mipmap/img_ob_two_one_back"
            android:visibility="invisible"
            app:layout_constraintBottom_toBottomOf="@id/tvContent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/tvContent"
            tools:visibility="visible" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvBtnSkip"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/qb_px_10"
            android:padding="@dimen/qb_px_10"
            android:text="@string/skip_jump_txt"
            android:textColor="@color/color_white"
            android:textSize="12sp"
            android:visibility="invisible"
            app:layout_constraintBottom_toBottomOf="@id/tvContent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/tvContent"
            tools:visibility="visible" />

        <com.hi.dhl.jprogressview.JProgressView
            android:id="@+id/jpvProgress"
            android:layout_width="@dimen/qb_px_44"
            android:layout_height="@dimen/qb_px_4"
            android:layout_marginTop="@dimen/qb_px_24"
            app:layout_constraintEnd_toStartOf="@id/img"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tvContent"
            app:progress_animate_duration="0"
            app:progress_color="@color/color_white"
            app:progress_color_background="@color/color_white_10"
            app:progress_paint_bg_width="@dimen/qb_px_2"
            app:progress_paint_value_width="@dimen/qb_px_2"
            app:progress_start_animate="false"
            app:progress_type="2"
            app:progress_value_max="100"
            tools:progress_value="50" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/img"
            android:layout_width="@dimen/qb_px_4"
            android:layout_height="@dimen/qb_px_4"
            android:layout_marginHorizontal="@dimen/qb_px_4"
            android:src="@mipmap/img_ob_21_jpv_progress_stage_no_select"
            app:layout_constraintBottom_toBottomOf="@id/jpvProgress"
            app:layout_constraintEnd_toStartOf="@id/jpvProgressOne"
            app:layout_constraintStart_toEndOf="@id/jpvProgress"
            app:layout_constraintTop_toTopOf="@id/jpvProgress" />

        <com.hi.dhl.jprogressview.JProgressView
            android:id="@+id/jpvProgressOne"
            android:layout_width="@dimen/qb_px_44"
            android:layout_height="@dimen/qb_px_4"
            app:layout_constraintBottom_toBottomOf="@id/img"
            app:layout_constraintEnd_toStartOf="@id/imgOne"
            app:layout_constraintStart_toEndOf="@id/img"
            app:layout_constraintTop_toTopOf="@id/img"
            app:progress_animate_duration="0"
            app:progress_color="@color/color_white"
            app:progress_color_background="@color/color_white_10"
            app:progress_paint_bg_width="@dimen/qb_px_2"
            app:progress_paint_value_width="@dimen/qb_px_2"
            app:progress_start_animate="false"
            app:progress_type="2"
            app:progress_value_max="100"
            tools:progress_value="50" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/imgOne"
            android:layout_width="@dimen/qb_px_4"
            android:layout_height="@dimen/qb_px_4"
            android:layout_marginHorizontal="@dimen/qb_px_4"
            android:src="@mipmap/img_ob_21_jpv_progress_stage_no_select"
            app:layout_constraintBottom_toBottomOf="@id/jpvProgressOne"
            app:layout_constraintEnd_toStartOf="@id/jpvProgressTwo"
            app:layout_constraintStart_toEndOf="@id/jpvProgressOne"
            app:layout_constraintTop_toTopOf="@id/jpvProgressOne" />

        <com.hi.dhl.jprogressview.JProgressView
            android:id="@+id/jpvProgressTwo"
            android:layout_width="@dimen/qb_px_44"
            android:layout_height="@dimen/qb_px_4"
            app:layout_constraintBottom_toBottomOf="@id/imgOne"
            app:layout_constraintEnd_toStartOf="@id/imgTwo"
            app:layout_constraintStart_toEndOf="@id/imgOne"
            app:layout_constraintTop_toTopOf="@id/imgOne"
            app:progress_animate_duration="0"
            app:progress_color="@color/color_white"
            app:progress_color_background="@color/color_white_10"
            app:progress_paint_bg_width="@dimen/qb_px_2"
            app:progress_paint_value_width="@dimen/qb_px_2"
            app:progress_start_animate="false"
            app:progress_type="2"
            app:progress_value_max="100"
            tools:progress_value="50" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/imgTwo"
            android:layout_width="@dimen/qb_px_4"
            android:layout_height="@dimen/qb_px_4"
            android:layout_marginHorizontal="@dimen/qb_px_4"
            android:src="@mipmap/img_ob_21_jpv_progress_stage_no_select"
            app:layout_constraintBottom_toBottomOf="@id/jpvProgressTwo"
            app:layout_constraintEnd_toStartOf="@id/jpvProgressThree"
            app:layout_constraintStart_toEndOf="@id/jpvProgressTwo"
            app:layout_constraintTop_toTopOf="@id/jpvProgressTwo" />

        <com.hi.dhl.jprogressview.JProgressView
            android:id="@+id/jpvProgressThree"
            android:layout_width="@dimen/qb_px_44"
            android:layout_height="@dimen/qb_px_4"
            app:layout_constraintBottom_toBottomOf="@id/imgOne"
            app:layout_constraintEnd_toStartOf="@id/imgThree"
            app:layout_constraintStart_toEndOf="@id/imgTwo"
            app:layout_constraintTop_toTopOf="@id/imgOne"
            app:progress_animate_duration="0"
            app:progress_color="@color/color_white"
            app:progress_color_background="@color/color_white_10"
            app:progress_paint_bg_width="@dimen/qb_px_2"
            app:progress_paint_value_width="@dimen/qb_px_2"
            app:progress_start_animate="false"
            app:progress_type="2"
            app:progress_value_max="100"
            tools:progress_value="50" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/imgThree"
            android:layout_width="@dimen/qb_px_4"
            android:layout_height="@dimen/qb_px_4"
            android:layout_marginHorizontal="@dimen/qb_px_4"
            android:src="@mipmap/img_ob_21_jpv_progress_stage_no_select"
            app:layout_constraintBottom_toBottomOf="@id/jpvProgressTwo"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/jpvProgressThree"
            app:layout_constraintTop_toTopOf="@id/jpvProgressTwo" />

        <androidx.constraintlayout.widget.Group
            android:id="@+id/groupJpvProgressStage"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="visible"
            app:constraint_referenced_ids="tvContent,jpvProgress,jpvProgressOne,jpvProgressTwo,jpvProgressThree,img,imgOne,imgTwo,imgThree" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>