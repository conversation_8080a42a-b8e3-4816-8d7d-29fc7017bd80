<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        tools:background="@mipmap/icon_welcome_bg"
        tools:padding="@dimen/qb_px_10">

        <com.coorchice.library.SuperTextView
            android:id="@+id/stvBg"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:stv_corner="@dimen/qb_px_10"
            app:stv_solid="@color/color_white_8"
            app:stv_stroke_color="@color/color_white_10"
            app:stv_stroke_width="@dimen/qb_px_1" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/img"
            android:layout_width="@dimen/qb_px_24"
            android:layout_height="@dimen/qb_px_24"
            android:layout_marginVertical="@dimen/qb_px_16"
            android:layout_marginStart="@dimen/qb_px_28"
            app:layout_constraintBottom_toBottomOf="@id/stvBg"
            app:layout_constraintStart_toStartOf="@id/stvBg"
            app:layout_constraintTop_toTopOf="@id/stvBg"
            tools:src="@mipmap/img_ob_21_1_1" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvContent"
            style="@style/Widget.App.Text.White.ExtraLargeContent"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:paddingHorizontal="@dimen/qb_px_16"
            app:layout_constraintBottom_toBottomOf="@id/img"
            app:layout_constraintEnd_toEndOf="@id/stvBg"
            app:layout_constraintStart_toEndOf="@id/img"
            app:layout_constraintTop_toTopOf="@id/img"
            tools:text="缓解疲劳恢复体力" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>