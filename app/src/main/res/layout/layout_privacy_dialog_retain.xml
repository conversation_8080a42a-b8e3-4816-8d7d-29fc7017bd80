<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/transparent_50">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="@dimen/qb_px_290"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:background="@drawable/download_bg_shape">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvTitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/qb_px_38"
            android:gravity="center"
            android:text="温馨提示"
            android:textColor="#081733"
            android:textSize="20sp"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="HardcodedText" />

        <TextView
            android:id="@+id/dialogTitle"
            style="@style/Widget.App.Text.Black.MediumContent"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/qb_px_16"
            android:layout_marginTop="@dimen/qb_px_24"
            android:lineSpacingMultiplier="1.2"
            android:text="@string/privacy_sorry_tips"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvTitle" />

        <com.coorchice.library.SuperTextView
            android:id="@+id/okTxt"
            style="@style/Widget.App.Text.White.LargeContent"
            android:layout_width="match_parent"
            android:layout_height="@dimen/qb_px_42"
            android:layout_marginHorizontal="@dimen/qb_px_30"
            android:layout_marginTop="@dimen/qb_px_24"
            android:gravity="center"
            android:text="@string/string_view_again"
            app:layout_constraintTop_toBottomOf="@+id/dialogTitle"
            app:stv_corner="@dimen/qb_px_27"
            app:stv_shaderEnable="true"
            app:stv_shaderEndColor="@color/color_7D6DEF"
            app:stv_shaderMode="topToBottom"
            app:stv_shaderStartColor="@color/color_5BA8FF" />

        <com.coorchice.library.SuperTextView
            android:id="@+id/cancelTxt"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/qb_px_16"
            android:gravity="center"
            android:text="@string/exit_app"
            android:textColor="@color/color_8d8d8d"
            android:textSize="12sp"
            app:layout_constraintTop_toBottomOf="@+id/okTxt" />

        <View
            android:layout_width="wrap_content"
            android:layout_height="@dimen/qb_px_32"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/cancelTxt" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</RelativeLayout>