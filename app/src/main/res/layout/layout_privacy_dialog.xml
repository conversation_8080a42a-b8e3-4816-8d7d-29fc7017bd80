<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/transparent_50">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="@dimen/qb_px_290"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:background="@drawable/download_bg_shape">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvTitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/qb_px_38"
            android:gravity="center"
            android:text="隐私政策保护"
            android:textColor="#081733"
            android:textSize="20sp"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="HardcodedText" />

        <ScrollView
            android:id="@+id/scroll_view"
            android:layout_width="match_parent"
            android:layout_height="@dimen/qb_px_120"
            android:layout_marginHorizontal="@dimen/qb_px_16"
            android:layout_marginTop="@dimen/qb_px_24"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvTitle">

            <com.sflin.csstextview.CSSTextView
                android:id="@+id/privacy_policy_content_txt"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:lineSpacingMultiplier="1.3"
                android:text="@string/string_privacy_content"
                android:textColor="@color/color_717A81"
                android:textSize="14sp" />

        </ScrollView>

        <com.coorchice.library.SuperTextView
            android:id="@+id/sure_txt"
            style="@style/Widget.App.Text.White.LargeContent"
            android:layout_width="0dp"
            android:layout_height="@dimen/qb_px_42"
            android:layout_marginHorizontal="@dimen/qb_px_30"
            android:layout_marginTop="@dimen/qb_px_24"
            android:gravity="center"
            android:text="@string/string_agree_and_continue"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/scroll_view"
            app:stv_corner="@dimen/qb_px_27"
            app:stv_shaderEnable="true"
            app:stv_shaderEndColor="@color/color_7D6DEF"
            app:stv_shaderMode="topToBottom"
            app:stv_shaderStartColor="@color/color_5BA8FF" />

        <com.coorchice.library.SuperTextView
            android:id="@+id/cancel_txt"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/qb_px_16"
            android:gravity="center"
            android:text="@string/string_disagree_txt"
            android:textColor="@color/color_8d8d8d"
            android:textSize="12sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/sure_txt" />

        <View
            android:layout_width="wrap_content"
            android:layout_height="@dimen/qb_px_32"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/cancel_txt" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</RelativeLayout>