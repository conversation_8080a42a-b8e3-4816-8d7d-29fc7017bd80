<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="wrap_content"
        android:layout_height="@dimen/qb_px_40"
        tools:ignore="SmallSp">

        <com.coorchice.library.SuperTextView
            android:id="@+id/stvBg"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:stv_corner="@dimen/qb_px_12"
            app:stv_solid="@color/color_0B97FF" />

        <com.coorchice.library.SuperTextView
            android:id="@+id/stvContent"
            style="@style/Widget.App.Text.White.SmallContent"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:layout_marginStart="@dimen/qb_px_12"
            android:gravity="center"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/stvLabel"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_goneMarginEnd="@dimen/qb_px_12"
            tools:text="精选声音" />

        <com.coorchice.library.SuperTextView
            android:id="@+id/stvLabel"
            android:layout_width="@dimen/qb_px_20"
            android:layout_height="@dimen/qb_px_20"
            android:layout_marginStart="@dimen/qb_px_4"
            android:layout_marginEnd="@dimen/qb_px_12"
            android:gravity="center"
            android:textColor="@color/color_0B97FF"
            android:textSize="10sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/stvContent"
            app:layout_constraintTop_toTopOf="parent"
            app:stv_corner="@dimen/qb_px_10"
            app:stv_solid="@color/color_white"
            tools:text="2" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>