<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:background="@mipmap/icon_welcome_bg">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/imgTop"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@mipmap/img_welcome_content"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.36" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/imgBottom"
            android:layout_width="0dp"
            android:layout_height="@dimen/qb_px_84"
            android:src="@color/color_white"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/img"
            android:layout_width="@dimen/qb_px_50"
            android:layout_height="@dimen/qb_px_50"
            android:src="@mipmap/img_welcome_logo"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/tvContent"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/imgBottom" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvContent"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/qb_px_8"
            android:text="@string/app_name"
            android:textColor="#0A1936"
            android:textSize="20sp"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="@id/img"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/img"
            app:layout_constraintTop_toTopOf="@id/img" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>