<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:background="@mipmap/icon_welcome_bg">

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/gl_hr"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            app:layout_constraintGuide_percent="0.16" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/imgTop"
            android:layout_width="@dimen/qb_px_116"
            android:layout_height="@dimen/qb_px_116"
            android:src="@mipmap/img_ob_welcome_top"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/gl_hr" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/imgCenter"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/qb_px_38"
            android:adjustViewBounds="true"
            android:alpha="0"
            android:src="@mipmap/img_ob_welcome_center"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/imgTop"
            tools:alpha="1" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/imgBottom"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/qb_px_30"
            android:layout_marginTop="@dimen/qb_px_30"
            android:adjustViewBounds="true"
            android:alpha="0"
            android:src="@mipmap/img_ob_welcome_bottom"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/imgCenter"
            tools:alpha="1" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>