<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:background="@mipmap/icon_welcome_bg"
        tools:ignore="HardcodedText">

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/gl_hr"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            app:layout_constraintGuide_percent="0.15" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvTitle"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/qb_px_30"
            android:textColor="@color/color_white"
            android:textSize="30sp"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/gl_hr"
            tools:text="你想通过一个好的睡眠\n获得什么？" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvSubTitle"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/qb_px_30"
            android:layout_marginTop="@dimen/qb_px_10"
            android:textColor="#CBDAF1"
            android:textSize="18sp"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tvTitle"
            tools:text="明确的问题，有助于为你提供更精准\n的解决方案" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerView"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/qb_px_16"
            android:layout_marginTop="@dimen/qb_px_30"
            android:overScrollMode="never"
            android:scrollbars="none"
            app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tvSubTitle"
            app:spanCount="2"
            tools:itemCount="4"
            tools:listitem="@layout/layout_item_single_choice_grid_img" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>