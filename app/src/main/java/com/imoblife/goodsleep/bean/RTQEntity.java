package com.imoblife.goodsleep.bean;

import android.os.Parcel;
import android.os.Parcelable;

import java.util.List;

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2025/8/11
 * 描   述：rtq - 问卷 - entity - 仿Now冥想
 */
public class RTQEntity implements Parcelable {

    public static final Creator<RTQEntity> CREATOR = new Creator<RTQEntity>() {
        @Override
        public RTQEntity createFromParcel(Parcel in) {
            return new RTQEntity(in);
        }

        @Override
        public RTQEntity[] newArray(int size) {
            return new RTQEntity[size];
        }
    };
    private List<QuestionListEntity> question_list;
    private CreatePlanEntity create_plan;

    protected RTQEntity(Parcel in) {
        question_list = in.createTypedArrayList(QuestionListEntity.CREATOR);
    }

    public List<QuestionListEntity> getQuestion_list() {
        return question_list;
    }

    public void setQuestion_list(List<QuestionListEntity> question_list) {
        this.question_list = question_list;
    }

    public CreatePlanEntity getCreate_plan() {
        return create_plan;
    }

    public void setCreate_plan(CreatePlanEntity create_plan) {
        this.create_plan = create_plan;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeTypedList(question_list);
    }

    public static class CreatePlanEntity implements Parcelable {
        public static final Creator<CreatePlanEntity> CREATOR = new Creator<CreatePlanEntity>() {
            @Override
            public CreatePlanEntity createFromParcel(Parcel in) {
                return new CreatePlanEntity(in);
            }

            @Override
            public CreatePlanEntity[] newArray(int size) {
                return new CreatePlanEntity[size];
            }
        };
        private List<QuestionListEntity> question;
        private List<String> bg_mg;

        protected CreatePlanEntity(Parcel in) {
            question = in.createTypedArrayList(QuestionListEntity.CREATOR);
            bg_mg = in.createStringArrayList();
        }

        public List<QuestionListEntity> getQuestion() {
            return question;
        }

        public void setQuestion(List<QuestionListEntity> question) {
            this.question = question;
        }

        public List<String> getBg_mg() {
            return bg_mg;
        }

        public void setBg_mg(List<String> bg_mg) {
            this.bg_mg = bg_mg;
        }

        @Override
        public int describeContents() {
            return 0;
        }

        @Override
        public void writeToParcel(Parcel dest, int flags) {
            dest.writeTypedList(question);
            dest.writeStringList(bg_mg);
        }
    }

    public static class QuestionListEntity implements Parcelable {

        private int question_id;
        private String title;
        private String subtitle;
        private int sequence;
        private String bottom_tips;
        private int type;
        private String img;
        private int group;
        // 与 group 一样，「ios」使用，4.6.8问卷开始往ios靠拢，也使用该字段
        private int rate_step;
        private String step_title;
        private float rate;
        private List<AnswerListEntity> answer_list;
        private boolean skip;
        // exp - 背景图
        private String img_bg;
        // exp - 底部img
        private String img_bottom;
        /*
            ob =>
            例如：1 - 某；
         */
        private int result_association_type;
        // exp - 0 - 引导页；1 - 题目页；2 - 生成计划页；3 - 过渡页
        private int page_type;
        // 问卷高亮标题
        private String highlight_title;
        // 阶段 - 过渡页 - 「question_type = 12」
        private int transitionStageValue;

        private int correspondingAssociation;

        // ob- 路由跳转「跳转过度页面」
        private boolean obThirtyRoute;

        // ob - 上一页为过渡页面 - 「返回时回退两页」
        private boolean previous_transition_page;

        protected QuestionListEntity(Parcel in) {
            question_id = in.readInt();
            title = in.readString();
            subtitle = in.readString();
            sequence = in.readInt();
            bottom_tips = in.readString();
            type = in.readInt();
            img = in.readString();
            group = in.readInt();
            rate_step = in.readInt();
            step_title = in.readString();
            rate = in.readFloat();
            answer_list = in.createTypedArrayList(AnswerListEntity.CREATOR);
            skip = in.readByte() != 0;
            img_bg = in.readString();
            img_bottom = in.readString();
            result_association_type = in.readInt();
            page_type = in.readInt();
            highlight_title = in.readString();
            transitionStageValue = in.readInt();
            correspondingAssociation = in.readInt();
            obThirtyRoute = in.readByte() != 0;
            previous_transition_page = in.readByte() != 0;
        }

        @Override
        public void writeToParcel(Parcel dest, int flags) {
            dest.writeInt(question_id);
            dest.writeString(title);
            dest.writeString(subtitle);
            dest.writeInt(sequence);
            dest.writeString(bottom_tips);
            dest.writeInt(type);
            dest.writeString(img);
            dest.writeInt(group);
            dest.writeInt(rate_step);
            dest.writeString(step_title);
            dest.writeFloat(rate);
            dest.writeTypedList(answer_list);
            dest.writeByte((byte) (skip ? 1 : 0));
            dest.writeString(img_bg);
            dest.writeString(img_bottom);
            dest.writeInt(result_association_type);
            dest.writeInt(page_type);
            dest.writeString(highlight_title);
            dest.writeInt(transitionStageValue);
            dest.writeInt(correspondingAssociation);
            dest.writeByte((byte) (obThirtyRoute ? 1 : 0));
            dest.writeByte((byte) (previous_transition_page ? 1 : 0));
        }

        @Override
        public int describeContents() {
            return 0;
        }

        public static final Creator<QuestionListEntity> CREATOR = new Creator<QuestionListEntity>() {
            @Override
            public QuestionListEntity createFromParcel(Parcel in) {
                return new QuestionListEntity(in);
            }

            @Override
            public QuestionListEntity[] newArray(int size) {
                return new QuestionListEntity[size];
            }
        };

        public boolean isPrevious_transition_page() {
            return previous_transition_page;
        }

        public void setPrevious_transition_page(boolean previous_transition_page) {
            this.previous_transition_page = previous_transition_page;
        }

        public boolean isObThirtyRoute() {
            return obThirtyRoute;
        }

        public void setObThirtyRoute(boolean obThirtyRoute) {
            this.obThirtyRoute = obThirtyRoute;
        }

        public int getCorrespondingAssociation() {
            return correspondingAssociation;
        }

        public void setCorrespondingAssociation(int correspondingAssociation) {
            this.correspondingAssociation = correspondingAssociation;
        }

        public int getQuestion_id() {
            return question_id;
        }

        public void setQuestion_id(int question_id) {
            this.question_id = question_id;
        }

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getSubtitle() {
            return subtitle;
        }

        public void setSubtitle(String subtitle) {
            this.subtitle = subtitle;
        }

        public int getSequence() {
            return sequence;
        }

        public void setSequence(int sequence) {
            this.sequence = sequence;
        }

        public String getBottom_tips() {
            return bottom_tips;
        }

        public void setBottom_tips(String bottom_tips) {
            this.bottom_tips = bottom_tips;
        }

        public int getType() {
            return type;
        }

        public void setType(int type) {
            this.type = type;
        }

        public String getImg() {
            return img;
        }

        public void setImg(String img) {
            this.img = img;
        }

        public int getGroup() {
            return group;
        }

        public void setGroup(int group) {
            this.group = group;
        }

        public int getRate_step() {
            return rate_step;
        }

        public void setRate_step(int rate_step) {
            this.rate_step = rate_step;
        }

        public String getStep_title() {
            return step_title;
        }

        public void setStep_title(String step_title) {
            this.step_title = step_title;
        }

        public float getRate() {
            return rate;
        }

        public void setRate(float rate) {
            this.rate = rate;
        }

        public List<AnswerListEntity> getAnswer_list() {
            return answer_list;
        }

        public void setAnswer_list(List<AnswerListEntity> answer_list) {
            this.answer_list = answer_list;
        }

        public boolean isSkip() {
            return skip;
        }

        public void setSkip(boolean skip) {
            this.skip = skip;
        }

        public String getImg_bg() {
            return img_bg;
        }

        public void setImg_bg(String img_bg) {
            this.img_bg = img_bg;
        }

        public String getImg_bottom() {
            return img_bottom;
        }

        public void setImg_bottom(String img_bottom) {
            this.img_bottom = img_bottom;
        }

        public int getResult_association_type() {
            return result_association_type;
        }

        public void setResult_association_type(int result_association_type) {
            this.result_association_type = result_association_type;
        }

        public int getPage_type() {
            return page_type;
        }

        public void setPage_type(int page_type) {
            this.page_type = page_type;
        }

        public String getHighlight_title() {
            return highlight_title;
        }

        public void setHighlight_title(String highlight_title) {
            this.highlight_title = highlight_title;
        }

        public int getTransitionStageValue() {
            return transitionStageValue;
        }

        public void setTransitionStageValue(int transitionStageValue) {
            this.transitionStageValue = transitionStageValue;
        }

        public static class AnswerListEntity implements Parcelable {
            private int answer_id;
            private String title;
            private String subtitle;
            private String img_name;
            private String img_name_select;
            private boolean whole_line;
            private String level;
            private String img_tip_name;
            // 本地自用
            private boolean selected = false;
            // 本地自用 - 得分
            private int score;

            public AnswerListEntity() {
            }

            protected AnswerListEntity(Parcel in) {
                answer_id = in.readInt();
                title = in.readString();
                subtitle = in.readString();
                img_name = in.readString();
                img_name_select = in.readString();
                whole_line = in.readByte() != 0;
                level = in.readString();
                img_tip_name = in.readString();
                selected = in.readByte() != 0;
                score = in.readInt();
            }

            @Override
            public void writeToParcel(Parcel dest, int flags) {
                dest.writeInt(answer_id);
                dest.writeString(title);
                dest.writeString(subtitle);
                dest.writeString(img_name);
                dest.writeString(img_name_select);
                dest.writeByte((byte) (whole_line ? 1 : 0));
                dest.writeString(level);
                dest.writeString(img_tip_name);
                dest.writeByte((byte) (selected ? 1 : 0));
                dest.writeInt(score);
            }

            @Override
            public int describeContents() {
                return 0;
            }

            public static final Creator<AnswerListEntity> CREATOR = new Creator<AnswerListEntity>() {
                @Override
                public AnswerListEntity createFromParcel(Parcel in) {
                    return new AnswerListEntity(in);
                }

                @Override
                public AnswerListEntity[] newArray(int size) {
                    return new AnswerListEntity[size];
                }
            };

            public String getImg_name_select() {
                return img_name_select;
            }

            public void setImg_name_select(String img_name_select) {
                this.img_name_select = img_name_select;
            }

            public int getScore() {
                return score;
            }

            public void setScore(int score) {
                this.score = score;
            }

            public String getImg_tip_name() {
                return img_tip_name;
            }

            public void setImg_tip_name(String img_tip_name) {
                this.img_tip_name = img_tip_name;
            }

            public String getLevel() {
                return level;
            }

            public void setLevel(String level) {
                this.level = level;
            }

            public boolean isSelected() {
                return selected;
            }

            public void setSelected(boolean selected) {
                this.selected = selected;
            }

            public int getAnswer_id() {
                return answer_id;
            }

            public void setAnswer_id(int answer_id) {
                this.answer_id = answer_id;
            }

            public String getTitle() {
                return title;
            }

            public void setTitle(String title) {
                this.title = title;
            }

            public String getSubtitle() {
                return subtitle;
            }

            public void setSubtitle(String subtitle) {
                this.subtitle = subtitle;
            }

            public String getImg_name() {
                return img_name;
            }

            public void setImg_name(String img_name) {
                this.img_name = img_name;
            }

            public boolean isWhole_line() {
                return whole_line;
            }

            public void setWhole_line(boolean whole_line) {
                this.whole_line = whole_line;
            }

        }

    }

}
