package com.imoblife.goodsleep.constant

import android.text.TextUtils
import com.imoblife.goodsleep.util.SpUtil

object ConsCommon {

    /**
     * 本次请求TOKEN
     */
    private var ACCESS_TOKEN = ""
    private const val BEARER = "Bearer "

    /**
     * 微信ID
     */
    const val WX_APP_ID = "wxf5b117d5e8bf1b51"

    /**
     * 小程序ID
     */
    const val WX_SMALL_PROGRAM_ID = "gh_ba01151eeda2"

    /**
     * 平台名称
     */
    const val PLATFORM = "android"

    // 视频缓存大小
    const val FILE_CACHE_SIZE = 10

    /**提前结束播放多久出现问卷 */
    const val PLAY_OVER_SECONDS = 60

    @JvmStatic
    val auth: String
        get() {
            if (TextUtils.isEmpty(ACCESS_TOKEN)) {
                setToken(SpUtil.getInstance().getStringValue(ConsSp.HTTPS_TOKEN, ""))
            }
            return if (TextUtils.isEmpty(ACCESS_TOKEN)) "" else BEARER + ACCESS_TOKEN
        }

    @JvmStatic
    fun setToken(token: String) {
        ACCESS_TOKEN = token
    }

    const val GENDER_MALE = "male"
    const val GENDER_FEMALE = "female"
    const val BIND_TYPE_QQ = "qq"
    const val BIND_TYPE_WX = "wechat"
    const val UNIONID = "unionid"


    /**
     * 大自然声类型
     */
    const val COURSE_TYPE_NATURE = "nature"


    // 网易七鱼AppId
    const val UNICORN_APP_ID = "2d91cbc2228ee52704545a4d7f952362"

    // bugly_release
    const val BUGLEY_RELEASE_APP_ID = "fdd0566550"

    // Sku - 模板id
    const val SKU_TEMPLATE_ID = "sku_template_id"

    // Sku - 模板 - 是否挽留
    const val SKU_RETENTION_BOOL = "sku_retention_bool"

    // Sku - 模板 - OB订阅页接口参数，如果点击的是svip课程加参数fromVipType=1
    const val SKU_COURSE_FROM_VIP_TYPE = "sku_course_from_vip_type"

    // 睡眠闹钟 - 时间
    const val SLEEP_ALARM_CLOCK_HOUR = "sleep_alarm_clock_hour"

    // 睡眠闹钟 - 分钟
    const val SLEEP_ALARM_CLOCK_MINUTE = "sleep_alarm_clock_minute"

    // 睡眠闹钟 - 音频title
    const val SLEEP_ALARM_CLOCK_SOUND_MUSIC_TITLE = "sleep_alarm_clock_sound_music_title_v4"

    // 睡眠闹钟 - 音频title - 默认
    const val SLEEP_ALARM_CLOCK_SOUND_MUSIC_TITLE_DEFAULT = "默认"

    // 睡眠闹钟 - 轻唤醒分钟数
    const val SLEEP_ALARM_CLOCK_LIGHT_AWAKENING_MINUTE = "sleep_alarm_clock_light_awakening_minute"

    // 睡眠闹钟 - 小睡分钟数
    const val SLEEP_ALARM_CLOCK_NAP_MINUTE = "sleep_alarm_clock_nap_minute"

    // 闹钟提醒 - 渐进唤醒模式
    const val ALARM_CLOCK_REMINDER_PROGRESSIVE_WAKE_UP_MODE =
        "alarm_clock_reminder_progressive_wake_up_mode"

    // 睡眠闹钟 - 闹钟铃声 - 某一音频
    const val SLEEP_ALARM_CLOCK_RINGING_TONE = "sleep_alarm_clock_ringing_tone_v2"

    // 睡眠闹钟 - 闹钟铃声 - 某一视频
    const val SLEEP_ALARM_CLOCK_RINGING_TONE_VIDEO = "sleep_alarm_clock_ringing_tone_video_v2"

    // 睡眠监测 - 提醒文案 - 不再显示
    const val SLEEP_MONITORING_TIPS_NOT_SHOW = "sleep_monitoring_tips_not_show"

    // 睡眠记录 - intent - entity
    const val SLEEP_RECORDING_CONTENT_ENTITY = "sleep_recording_content_entity"

    // 睡眠闹钟 - 闹钟铃声 - 某一音频 - 默认 - url
    const val SLEEP_ALARM_CLOCK_RINGING_TONE_URL = "asset:///music_ringtone_1.mp3"

    // 睡眠监测 - 设置入睡时间 - dialog - activity - 时
    const val SET_SLEEP_TIME_HOUR = "set_sleep_time_hour"

    // 睡眠监测 - 设置入睡时间 - dialog - activity - 分
    const val SET_SLEEP_TIME_MINUTE = "set_sleep_time_minute"

    // 睡眠监测 - 设置入睡时间 - dialog - activity - 弹出
    const val SET_SLEEP_TIME_DIALOG_ACTIVITY = "set_sleep_time_dialog_activity"

    // 睡眠闹钟 - 设置起床时间 - cursor
    const val SET_THE_WAKE_UP_TIME_CURSOR = "set_the_wake_up_time_cursor"

    // 睡眠闹钟 - 设置起床时间 - content
    const val SET_THE_WAKE_UP_TIME_CONTENT = "set_the_wake_up_time_content"

    // 睡眠闹钟 - 设置小睡时间 - cursor
    const val SET_NAP_CURSOR = "set_nap_cursor"

    // 睡眠闹钟 - 设置小睡时间 - content
    const val SET_NAP_TIME_CONTENT = "set_nap_time_content"

    // 睡眠闹钟 - checkBox - state
    const val SLEEP_ALARM_CLOCK_CHECK_STATE = "sleep_alarm_clock_check_state"

    //设置入睡时间
    const val SET_SLEEP_TIME = "set_sleep_time"

    //设置入睡提醒
    const val SET_SLEEP_STATUS = "set_sleep_status"


    // 设置入睡提醒 - 如用户累计关闭3次以上、该版本不再出现
    const val SET_SLEEP_STATUS_CLOSE_MECHANISM = "set_sleep_status_close_mechanism"


    // EXP - 实验性问卷 - 4.4.8 - 调整 => 华为线上组问卷的左右滑动的选项改成，左图右文的样式(type == 10)
    const val EXP_QUESTIONNAIRE_DETAIL_LEFT_PICTURE_AND_RIGHT_TEXT =
        "exp_questionnaire_detail_left_picture_and_right_text"


    // EXP - 实验性问卷 - 冥想水平
    const val EXP_QUESTIONNAIRE_MEDITATION_LEVEL = "exp_questionnaire_meditation_level"

    // EXP - 实验性问卷 - 睡眠质量
    const val EXP_QUESTIONNAIRE_SLEEP_QUALITY = "exp_questionnaire_sleep_quality"

    // EXP - 实验性问卷 - 8宫格多选答案元素第一个 - 对应生成某种计划（例如幸福、抗衰老）
    const val EXP_QUESTIONNAIRE_GENERATE_PLAN_ELEMENT_FIRST =
        "exp_questionnaire_generate_plan_element_first"

    // transition - 问卷 - 睡眠时长 - level
    const val TRANSITION_QUESTIONNAIRE_SLEEP_DURATION_LEVEL =
        "transition_questionnaire_sleep_duration_level"

    // transition - 问卷 - 入睡困难程度｜ob - 15 - 入睡速度
    const val TRANSITION_QUESTIONNAIRE_DIFFICULTY_IN_FALLING_ASLEEP =
        "transition_questionnaire_difficulty_in_falling_asleep"

    // transition - 问卷 - 夜间醒来情况｜ob - 15 - 睡眠障碍
    const val TRANSITION_QUESTIONNAIRE_WAKE_UP_SITUATION_AT_NIGHT =
        "transition_questionnaire_wake_up_situation_at_night"

    // transition - 问卷 - 清晨早醒情况
    const val TRANSITION_QUESTIONNAIRE_EARLY_MORNING_AWAKENING_SITUATION =
        "transition_questionnaire_early_morning_awakening_situation"

    // transition - 问卷 - 被诊断的情况｜睡眠问题｜影响睡眠的习惯
    const val TRANSITION_QUESTIONNAIRE_DIAGNOSED_CONDITION =
        "transition_questionnaire_diagnosed_condition"

    // transition - 问卷 - 你对自己睡眠质量的满意程度如何？
    const val TRANSITION_QUESTIONNAIRE_SLEEP_QUALITY_SATISFACTION_LEVEL =
        "transition_questionnaire_sleep_quality_satisfaction_level"

    // transition - 问卷 - 睡眠质量
    const val TRANSITION_QUESTIONNAIRE_SLEEP_QUALITY =
        "transition_questionnaire_sleep_quality"

    // transition - 问卷 - 性别
    const val TRANSITION_QUESTIONNAIRE_GENDER = "transition_questionnaire_gender"

    // transition - 问卷 - 年龄
    const val TRANSITION_QUESTIONNAIRE_AGE = "transition_questionnaire_age"

    // silent - 问卷 - 入睡时间
    const val SILENT_QUESTIONNAIRE_SLEEP_TIME = "silent_questionnaire_sleep_time"

    // silent - 问卷 - 睡眠情况
    const val SILENT_QUESTIONNAIRE_SLEEP_STATUS = "silent_questionnaire_sleep_status"

    // ob - 15 - 睡眠时长
    const val OB_FIFTEEN_SLEEP_DURATION = "ob_fifteen_sleep_duration"

    // ob - 15 - 睡眠效率
    const val OB_FIFTEEN_SLEEP_EFFICIENCY = "ob_fifteen_sleep_efficiency"

    // ob - 15 - 日间功能障碍
    const val OB_FIFTEEN_DAYTIME_DYSFUNCTION = "ob_fifteen_daytime_dysfunction"

    // ob - 15 - 日间功能障碍 - 状态
    const val OB_FIFTEEN_DAYTIME_DYSFUNCTION_STATE = "ob_fifteen_daytime_dysfunction_state"

    // ob - 15 - 白天心情如何
    const val OB_FIFTEEN_DAY_MOOD = "ob_fifteen_day_mood"

    //首页顶部分类
    const val HOME_CATEGORY_HOME_TAB = "home_tab"

    // 首页腰部banner
    const val HOME_CATEGORY_BANNER = "home_banner"

    //首页推荐计划
    const val HOME_CATEGORY_USER_PLAN = "user_plan"

    // 首页 - 睡眠历程
    const val HOME_CATEGORY_SLEEP_TAB = "sleep_tab"

    // 首页 - 助眠声音
    const val HOME_CATEGORY_TAG_COURSE_LIST = "tag_course_list"

    // 首页 - 所有助眠声音 - tab
    const val HOME_CATEGORY_TAG_COURSE_V2 = "tag_course_v2"

    // 首页 - 混合白噪音
    const val HOME_CATEGORY_MIX_WHITE_NOISE = "mix_white_noise"

    // 首页 - 梦境解析
    const val HOME_CATEGORY_DREAM_ANALYSIS = "dream_analysis"

    // 首页 - 睡眠科普
    const val HOME_CATEGORY_SLEEP_ARTICLE = "sleep_article"

    // 首页 - 每日寄语
    const val HOME_CATEGORY_EVERY_DAY_MESSAGE = "every_day_message"

    //首页白噪音分类
    const val HOME_CATEGORY_WHITE_NOISE = "white_noise"

    //首页睡眠知识分类
    const val HOME_CATEGORY_TAG_ARTICLE = "tag_article"

    //首页睡眠内容分类
    const val HOME_CATEGORY_TAG_COURSE = "tag_course"

    //首页计划类型-广告
    const val HOME_PLAN_TAG_AD = "plan_tag_ad"

    //首页计划类型-课程
    const val HOME_PLAN_TAG_COURSE = "plan_tag_course"

    // ob问卷 - 沉默用户
    const val OB_QUESTIONNAIRE_DETAIL_STAGE_SILENT_USER =
        "ob_questionnaire_detail_stage_silent_user"

    // 问卷 - question_type = 8
    const val QUESTIONNAIRE_QUESTION_TYPE_EIGHT_STAGE = "questionnaire_question_type_eight_stage"

    // ob问卷 - 5 - tip - 提示
    const val QUESTIONNAIRE_QUESTION_TYPE_NOW_STAGE = "questionnaire_question_type_now_stage"

    // ob问卷 - 6 - hint - 提示
    const val QUESTIONNAIRE_QUESTION_TYPE_NOW_STAGE_HINT =
        "questionnaire_question_type_now_stage_hint"

    // ob问卷 - 8 - transition - 过渡
    const val QUESTIONNAIRE_QUESTION_TYPE_NOW_STAGE_TRANSITION =
        "questionnaire_question_type_now_stage_transition"

    // ob问卷 - 9 - 沉默用户
    const val QUESTIONNAIRE_STAGE_SILENT_USERS = "questionnaire_stage_silent_users"

    // ob问卷 - 10 - transition - 过渡 - 在8基础上调整
    const val QUESTIONNAIRE_QUESTION_TYPE_NOW_STAGE_TRANSITION_TEN =
        "questionnaire_question_type_now_stage_transition_ten"

    // ob问卷 - 11 - transition - 过渡 - 在10基础上调整
    const val QUESTIONNAIRE_QUESTION_TYPE_NOW_STAGE_TRANSITION_ELEVEN =
        "questionnaire_question_type_now_stage_transition_eleven"

    // ob问卷 - 12 - transition - 过渡 - 在10基础上调整
    const val QUESTIONNAIRE_QUESTION_TYPE_NOW_STAGE_TRANSITION_TWELVE =
        "questionnaire_question_type_now_stage_transition_twelve"

    // ob问卷 - 13 - hint - 提示 - 在6基础上调整
    const val QUESTIONNAIRE_QUESTION_TYPE_NOW_STAGE_TRANSITION_THIRTEEN =
        "questionnaire_question_type_now_stage_transition_thirteen"

    // ob问卷 - 15 - 在12基础上调整
    const val QUESTIONNAIRE_QUESTION_TYPE_NOW_STAGE_FIFTEEN =
        "questionnaire_question_type_now_stage_fifteen"

    // ob - 18 - stage
    const val OB_SIXTEEN_STAGE = "ob_sixteen_stage"

    // ob - 18 - stage - type「睡眠分析｜精力分析」
    const val OB_SIXTEEN_STAGE_TYPE = "ob_sixteen_stage_type"

    // ob - 18 - json
    const val OB_SIXTEEN_JSON = "ob_sixteen_json"

    // ob - 18 - json - 精力分析
    const val OB_SIXTEEN_JSON_ENERGY_ANALYSIS = "ob_sixteen_json_energy_analysis"

    // ob - 21 - json
    const val OB_TWENTY_ONE = "ob_twenty_one_json"


    /*定义页面跳转路由类型-----------------------------------------START*/

    //路由--我的收藏
    const val ROUT_COLLECT = "rout_tag_collect"

    //路由--播放记录
    const val ROUT_PLAY_RECORD = "rout_tag_play_record"

    //路由--课程分类
    const val ROUT_COURSE_CATEGORY = "rout_tag_course_category"

    //路由--睡眠百科
    const val ROUT_ARTICLE_CATEGORY = "rout_tag_sleep_article"

    //路由--订阅中心
    const val ROUT_VIP_CENTER = "rout_tag_vip_center"

    //路由--直接支付
    const val ROUT_VIP_PAY = "rout_tag_vip_pay"

    //路由--H5页面
    const val ROUT_WEB_PAGE = "rout_tag_web_page"

    //路由--梦境解析
    const val ROUT_DREAM_ANALYSIS = "rout_tag_dream_analysis"

    // 路由--睡前清单
    const val ROUT_BEDTIME_CHECKLIST = "rout_tag_bedtime_checklist"

    // 路由--心情日记
    const val ROUT_MOOD_DIARY = "rout_tag_mood_diary"

    // 路由--呼吸练习
    const val ROUT_BREATHING_EXERCISES = "rout_tag_breathing_exercises"

    // 路由 -- 心率监测
    const val ROUT_HEART_RATE_MONITORING = "heart_rate_monitoring"

    // 路由 -- 挑战赛
    const val ROUT_CHALLENGE_PAGE = "challenge"

    // 路由 -- 睡前仪式
    const val ROUT_BEDTIME_CEREMONY = "bedtime_ceremony"

    // 路由--呼吸练习
    const val ROUT_BREATHING = "rout_tag_breathing"

    /*定义页面跳转路由类型-----------------------------------------END*/

    /*定义课程类型-----------------------------------------START*/

    //课程类型--白噪音
    const val COURSE_TYPE_WHITE_NOISE = "white_noise"

    //课程类型--助眠音乐
    const val COURSE_TYPE_MUSIC = "sleep_music"

    //课程类型--助眠脑波
    const val COURSE_TYPE_BRAIN_WAVE = "brain_wave"

    //课程类型--助眠冥想
    const val COURSE_TYPE_MEDITATION = "meditation"

    //课程类型--睡眠故事
    const val COURSE_TYPE_STORY = "sleep_story"

    /*定义课程类型--------------------------------------------END*/

    // app配置缓存项
    const val AppConfigVersion = "AppConfig_V1"

    /*定义订单页面来源-----------------------------------------START*/

    // OB订阅页面
    const val ORDER_SOURCE_OB_CENTER = 1

    // OB挽留弹框
    const val ORDER_SOURCE_OB_RETENTION = 2

    // 首页全屏弹框
    const val ORDER_SOURCE_HOME_FULL_ALTER = 3

    // 首页会员计划推荐
    const val ORDER_SOURCE_HOME_PLAN_VIP = 4

    // 订阅中心
    const val ORDER_SOURCE_VIP_CENTER = 5

    // 课程模块 - 首页全屏弹框
    const val ORDER_SOURCE_COURSE_HOME_FULL_ALTER = 6

    // 课程模块 - OB订阅页面
    const val ORDER_SOURCE_COURSE_OB_CENTER = 7

    // 睡眠报告页面 - 天 - 立即解锁你的睡眠报告
    const val ORDER_SOURCE_DAY_UNLOCK_SLEEP_REPORT = 8

    // 试听页面
    const val ORDER_SOURCE_TRIAL_LISTENING_COURSE_PAGE = 9

    // 首次日报告页面
    const val ORDER_SOURCE_FIRST_DAILY_REPORT_PAGE = 10

    // 全屏挽留
    const val ORDER_SOURCE_FULL_SCREEN_RETENTION = 11

    // 订阅中心挽留
    const val ORDER_SOURCE_SUBSCRIPTION_CENTER_RETENTION = 12

    // 二次全屏
    const val ORDER_SOURCE_SECONDARY_FULL_SCREEN = 13

    // 二次全屏挽留
    const val ORDER_SOURCE_SECONDARY_FULL_SCREEN_RETENTION = 14

    // 心率监测 - 底部Dialog
    const val ORDER_SOURCE_HEART_RATE = 16

    // sku - 睡眠报告 - ad - 动图轮播
    const val ORDER_SOURCE_VIP_SKU_SLEEP_REPORT = 17

    // 非当日睡眠报告付费页挽留弹窗｜Vip - Sku - 睡眠报告 - ad - 动图轮播
    const val ORDER_SOURCE_VIP_SKU_SLEEP_REPORT_RETENTION = 18

    // 二次OB订阅页面
    const val ORDER_SOURCE_SECONDARY_OB_SUBSCRIPTION_PAGE = 19

    // 二次OB挽留弹框
    const val ORDER_SOURCE_SECONDARY_OB_RETENTION_BULLET_BOX = 20

    /*定义订单页面来源-----------------------------------------END*/

    /*定义下单 - 前置资源位置 - 来源-----------------------------------------START*/

    // 睡眠日报立即解锁按钮
    const val SLEEP_DAILY_IMMEDIATELY_UNLOCK_BUTTON = 1

    /*定义下单 - 前置资源位置 - 来源-----------------------------------------END*/

    // 呼吸练习 - 呼吸背景 - 选中
    const val BREATH_TIMING_BG_SELECT_STATUS = "breath_timing_bg_select_status"

    // 呼吸练习 - 呼吸背景 - 选中 - img
    const val BREATH_TIMING_BG_SELECT_IMG = "breath_timing_bg_select_img"

    // 呼吸练习 - 呼吸模式 - id
    const val BREATH_TIMING_MODE_SELECT_STATUS = "breath_timing_mode_select_status"

    // 呼吸练习 - 呼吸模式 - name
    const val BREATH_TIMING_MODE_NAME = "breath_timing_mode_name"

    // 呼吸练习 - 时长
    const val BREATH_TIMING_DURATION = "breath_timing_duration"

    // 呼吸练习 - 时长 - position
    const val BREATH_TIMING_DURATION_POSITION = "breath_timing_duration_position"

    // 呼吸练习 - 呼吸模式 - 数据缓存
    const val BREATH_TIMING_MODE_DATA_CACHE = "breath_timing_mode_data_cache"

    // 呼吸练习 - 呼吸背景 - 数据缓存
    const val BREATH_TIMING_BG_DATA_CACHE = "breath_timing_bg_data_cache"

    // 呼吸练习 - 类型 - Song
    const val SONG_TYPE_BREATHING_PRACTICE = "song_type_breathing_practice"

    // 呼吸练习 - 类型 - 上报类型
    const val SONG_TYPE_BREATHING_PRACTICE_BREATH = "breath"

    // 呼吸练习 - 上报 - onlyId
    const val INTENT_TYPE_BREATHING_ONLY_ID = "intent_type_breathing_only_id"

    // 问卷引导背景音 - 默认 - url
    const val HEART_BMP_SOUND_CARDIAC_TESTING = "asset:///sound_cardiac_testing.mp3"

    // banner - item - holder - tag
    const val VIDEO_BANNER_ITEM_HOLDER_TAG = "video_banner_item_holder_tag"

}