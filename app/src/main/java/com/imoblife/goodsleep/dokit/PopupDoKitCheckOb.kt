package com.imoblife.goodsleep.dokit

import android.annotation.SuppressLint
import android.content.Context
import androidx.databinding.DataBindingUtil
import com.imoblife.goodsleep.ActivityStackManager
import com.imoblife.goodsleep.R
import com.imoblife.goodsleep.activity.questionnaire.ObArgs
import com.imoblife.goodsleep.activity.questionnaire.exp.EXPQuestionnaireSilentUserActivity
import com.imoblife.goodsleep.activity.questionnaire.ob.ObFifteenGuideActivity
import com.imoblife.goodsleep.activity.questionnaire.silent.SilentUsersGuideActivity
import com.imoblife.goodsleep.activity.questionnaire.sixteen.ObSixteenGuideActivity
import com.imoblife.goodsleep.activity.questionnaire.tip.TipQuestionGuideActivity
import com.imoblife.goodsleep.activity.questionnaire.transition.TransitionQuestionGuideActivity
import com.imoblife.goodsleep.activity.questionnaire.transition.TransitionQuestionnaireGuideActivity
import com.imoblife.goodsleep.activity.questionnaire.twentyone.ObTwentyOneVpActivity
import com.imoblife.goodsleep.constant.ConsSp
import com.imoblife.goodsleep.databinding.LayoutViewCheckObAndSkipBinding
import com.imoblife.goodsleep.ext.onDebounceClickListener
import com.imoblife.goodsleep.ext.startVipSkuTemplatedActivity
import com.imoblife.goodsleep.model.ConfigMgr
import com.imoblife.goodsleep.repository.ConfigRepository
import com.imoblife.goodsleep.util.SpUtil
import com.imoblife.goodsleep.util.ToastUtils
import com.lxj.xpopup.core.CenterPopupView

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2025/6/26
 * 描   述：DoKit - 切换ob｜跳过 - Popup
 */
class PopupDoKitCheckOb(context: Context) : CenterPopupView(context) {

    override fun getImplLayoutId() = R.layout.layout_view_check_ob_and_skip

    @SuppressLint("SetTextI18n")
    override fun onCreate() {
        super.onCreate()
        DataBindingUtil.bind<LayoutViewCheckObAndSkipBinding>(popupImplView)?.apply {
            val ac = ActivityStackManager.getInstance().currentActivity
            tvCurrentOb.text = "当前服务器下发问卷为 - ${
                SpUtil.getInstance().getIntValue(ConsSp.SP_KEY_CURRENT_OB_QUESTION_TYPE, -1)
            }"

            tvOne.onDebounceClickListener { gotoOb(1) }
            tvFive.onDebounceClickListener { gotoOb(5) }
            tvSix.onDebounceClickListener { gotoOb(6) }
            tvSeven.onDebounceClickListener { gotoOb(7) }
            tvEight.onDebounceClickListener { gotoOb(8) }
            tvNine.onDebounceClickListener { gotoOb(9) }
            tvTen.onDebounceClickListener { gotoOb(10) }
            tvEleven.onDebounceClickListener { gotoOb(11) }
            tvTwelve.onDebounceClickListener { gotoOb(12) }
            tvThirteen.onDebounceClickListener { gotoOb(13) }
            tvFifteen.onDebounceClickListener { gotoOb(15) }
            tvEighteen.onDebounceClickListener { gotoOb(18) }
            tvTwentyOne.onDebounceClickListener { gotoOb(21) }

            tvSkip.onDebounceClickListener {
                ConfigRepository().updateConfig { _ ->
                    ac.startVipSkuTemplatedActivity()
                }
            }
            tvCache.onDebounceClickListener {
                cleanCacheData()
                ToastUtils.showShortToast("已清空")
            }
        }
    }

    private fun gotoOb(questionType: Int) {
        val ac = ActivityStackManager.getInstance().currentActivity
        val config = ConfigMgr.getInstance().config
        config.question_type = questionType
        ConfigMgr.getInstance().saveConfig(config)
        when (questionType) {
            // ob问卷 - 沉默用户
            1 -> {
                EXPQuestionnaireSilentUserActivity.startActivity(ac)
                ActivityStackManager.getInstance()
                    .finishOtherActivity(EXPQuestionnaireSilentUserActivity::class.java)
            }
            // 5 - ob问卷 - tip - 提示
//            5 -> TipQuestionGuideActivity.startActivity(ac)
            // 6 - ob问卷 - hint - 提示
//            6 -> HintQuestionGuideActivity.startActivity(ac)
            /*
                5 & 6 => ob问卷 - 提示 - 引导
                7 => 基于6，调整计划生成页 - 「https://www.tapd.cn/tapd_fe/51322357/story/detail/1151322357001004097」
                13 => ob问卷 - 提示 - 引导 - 在6基础上调整「调整bg」
             */
            5, 6, 7, 13 -> {
                TipQuestionGuideActivity.startActivity(ac)
                ActivityStackManager.getInstance()
                    .finishOtherActivity(TipQuestionGuideActivity::class.java)
            }
            /*
                8 => ob问卷 - transition - 引导
                10 => ob问卷 - transition - 引导 - 在8基础上调整
                12 => ob问卷 - transition - 引导 - 在10基础上调整
             */
            8, 10, 12 -> {
                TransitionQuestionnaireGuideActivity.startActivity(ac)
                ActivityStackManager.getInstance()
                    .finishOtherActivity(TransitionQuestionnaireGuideActivity::class.java)
            }
            // 9 => ob问卷 - 沉默用户 - 引导
            9 -> {
                SilentUsersGuideActivity.startActivity(ac)
                ActivityStackManager.getInstance()
                    .finishOtherActivity(SilentUsersGuideActivity::class.java)
            }
            // 11 => ob问卷 - transition - 引导 - 在10基础上调整
            11 -> {
                TransitionQuestionGuideActivity.startActivity(ac)
                ActivityStackManager.getInstance()
                    .finishOtherActivity(TransitionQuestionGuideActivity::class.java)
            }
            // 15 => 在12基础上调整
            15 -> {
                ObFifteenGuideActivity.startActivity(ac)
                ActivityStackManager.getInstance()
                    .finishOtherActivity(ObFifteenGuideActivity::class.java)
            }
            // 18 => 在6基础上调整
            18 -> {
                ObSixteenGuideActivity.startActivity(ac)
                ActivityStackManager.getInstance()
                    .finishOtherActivity(ObSixteenGuideActivity::class.java)
            }
            // 21 => 全新 - 【OB问卷优化】https://www.tapd.cn/tapd_fe/51322357/story/detail/1151322357001005831
            21 -> {
                ObTwentyOneVpActivity.startActivity(ac)
                ActivityStackManager.getInstance()
                    .finishOtherActivity(ObTwentyOneVpActivity::class.java)
            }

            else -> {}
        }
    }

    /**
     * 移除ob缓存数据
     *
     * @see ObArgs
     */
    private fun cleanCacheData() {
        // 动画执行中
        ObArgs.mIsAnimationRunning = false

        // 性别
        ObArgs.mGender = ""

        // 年龄
        ObArgs.mAge = ""

        // 选择个性化您的计划 - 「rv所选item - position」
        ObArgs.mSelectYourPlan = -1

        // 冥想水平 - 等级
        ObArgs.mMeditationLevel = ""

        // 睡眠质量
        ObArgs.mSleepQuality = ""

        // 压力水平｜压力指数
        ObArgs.mPressureLevel = ""

        // 内耗指数
        ObArgs.mInternalFrictionIndex = ""

        // 情绪管理
        ObArgs.mEmotionalManagement = ""

        // ob - 21 - 每个题 - 得分
        ObArgs.mapScore = hashMapOf<Int, Int>()
    }

}