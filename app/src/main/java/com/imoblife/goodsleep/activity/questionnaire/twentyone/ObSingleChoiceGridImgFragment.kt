package com.imoblife.goodsleep.activity.questionnaire.twentyone

import android.annotation.SuppressLint
import android.text.TextUtils
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import com.imoblife.goodsleep.R
import com.imoblife.goodsleep.activity.questionnaire.ObQuestionnaireViewModel
import com.imoblife.goodsleep.adapter.decoration.GridSpaceItemDecoration
import com.imoblife.goodsleep.bean.RTQEntity.QuestionListEntity
import com.imoblife.goodsleep.databinding.LayoutFmObRvGridImgBinding
import com.imoblife.goodsleep.ext.args
import com.imoblife.goodsleep.ext.dp
import com.imoblife.goodsleep.ext.removeAnim
import com.imoblife.goodsleep.mvvm.BaseVMFragment
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2025/8/12
 * 描   述：ob - 问卷 - 问卷详情页 - 单选 - item - Grid - Span - 2 - img - fragment
 */
class ObSingleChoiceGridImgFragment : BaseVMFragment<ObQuestionnaireViewModel>() {

    companion object {

        fun newInstance(entity: QuestionListEntity) =
            ObSingleChoiceGridImgFragment().apply { mEntity = entity }

    }

    private var mEntity: QuestionListEntity by args()

    private lateinit var mBind: LayoutFmObRvGridImgBinding

    private val mAdapter by lazy(LazyThreadSafetyMode.NONE) {
        ObSingleChoiceGridImgAdapter { entity -> nextPage(entity) }
    }

    /**
     * 下一题
     */
    private fun nextPage(entity: QuestionListEntity.AnswerListEntity) {
        lifecycleScope.launch {
            delay(200)
            mViewModel.apply {
                obCommitAnswer(mEntity.question_id, 2, entity.answer_id.toString())
                nextVpPage()
            }
        }
    }

    override fun getLayoutResId() = R.layout.layout_fm_ob_rv_grid_img

    override fun initVM() =
        ViewModelProvider(requireActivity())[ObQuestionnaireViewModel::class.java]

    @SuppressLint("NotifyDataSetChanged")
    override fun initView() {
        mBind = mBinding as LayoutFmObRvGridImgBinding
        mBind.apply {
            tvTitle.text = mEntity.title
            if (!TextUtils.isEmpty(mEntity.subtitle)) {
                tvSubTitle.text = mEntity.subtitle
            }
            recyclerView.apply {
                removeAnim()
                adapter = mAdapter
                addItemDecoration(GridSpaceItemDecoration(2, 16.dp, 16.dp))
            }
            mAdapter.submitList(mEntity.answer_list)
        }
    }

    override fun onFragmentFirstVisible() {
        super.onFragmentFirstVisible()
        mViewModel.obCommitAnswer(mEntity.question_id, 1)
    }

    override fun initData() {}

    override fun startObserve() {}

}