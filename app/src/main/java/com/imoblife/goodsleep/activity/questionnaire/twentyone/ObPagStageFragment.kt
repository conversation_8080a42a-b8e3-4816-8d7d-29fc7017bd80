package com.imoblife.goodsleep.activity.questionnaire.twentyone

import androidx.lifecycle.ViewModelProvider
import com.imoblife.goodsleep.R
import com.imoblife.goodsleep.activity.questionnaire.ObArgs
import com.imoblife.goodsleep.activity.questionnaire.ObQuestionnaireViewModel
import com.imoblife.goodsleep.bean.RTQEntity
import com.imoblife.goodsleep.databinding.LayoutFmObPagStageBinding
import com.imoblife.goodsleep.ext.args
import com.imoblife.goodsleep.mvvm.BaseVMFragment
import org.libpag.PAGFile
import org.libpag.PAGView

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2025/8/11
 * 描   述：ob - 问卷 - 问卷详情页 - 阶段状态页面 - fragment
 */
class ObPagStageFragment : BaseVMFragment<ObQuestionnaireViewModel>() {

    private lateinit var mBind: LayoutFmObPagStageBinding

    private var mEntity: RTQEntity.QuestionListEntity by args()

    companion object {

        fun newInstance(entity: RTQEntity.QuestionListEntity) = ObPagStageFragment().apply {
            mEntity = entity
        }

    }

    override fun getLayoutResId() = R.layout.layout_fm_ob_pag_stage

    override fun initVM() =
        ViewModelProvider(requireActivity())[ObQuestionnaireViewModel::class.java]

    override fun initView() {
        mBind = mBinding as LayoutFmObPagStageBinding
    }

    override fun onFragmentResume() {
        super.onFragmentResume()
        mBind.pagImgStage.post {
            mBind.pagImgStage.play()
        }
    }

    override fun initData() {
        mBind.pagImgStage.apply {
            val pagFile =
                PAGFile.Load(requireActivity().assets, "ob_21_pag_${mEntity.rate_step}.pag")
            composition = pagFile
            setRepeatCount(1)
            addListener(object : PAGView.PAGViewListener {
                override fun onAnimationStart(p0: PAGView?) {
                    ObArgs.mIsAnimationRunning = true
                }

                override fun onAnimationEnd(p0: PAGView?) {
                    ObArgs.mIsAnimationRunning = false
                    goQA()
                }

                override fun onAnimationCancel(p0: PAGView?) {
                    ObArgs.mIsAnimationRunning = false
                    goQA()
                }

                override fun onAnimationRepeat(p0: PAGView?) {
                }

                override fun onAnimationUpdate(p0: PAGView?) {
                }
            })
        }
    }

    override fun startObserve() {}

    /**
     * 路由 - QA
     */
    private fun goQA() {
        mViewModel.apply {
            obCommitAnswer(mEntity.question_id, 1)
            nextVpPage()
        }
    }

}