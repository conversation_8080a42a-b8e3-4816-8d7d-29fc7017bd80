package com.imoblife.goodsleep.activity.questionnaire

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.imoblife.goodsleep.bean.ObEntity
import com.imoblife.goodsleep.mvvm.BaseViewModel
import com.imoblife.goodsleep.mvvm.UiStatus

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2023/3/4
 * 描   述：ob问卷 - ViewModel
 */
class ObQuestionnaireViewModel : BaseViewModel<Any?>() {

    private val mRepository by lazy(LazyThreadSafetyMode.NONE) { ObQuestionnaireRepository() }

    // 问卷 - data
    private val _questionnaireData = MutableLiveData<UiStatus<List<ObEntity>>>()
    val questionnaireData: LiveData<UiStatus<List<ObEntity>>> = _questionnaireData

    // 问卷 - vp - 当前页面
    private val _vpCurrentPage = MutableLiveData<Int>()
    val vpCurrentPage: LiveData<Int> = _vpCurrentPage

    /**
     * 问卷 - data
     */
    fun getQuestionnaireData() {
        mRepository.getQuestionnaireData(_questionnaireData)
    }

    /**
     * 问卷 - 提交答案（答题）
     *
     * @param question_id 题目ID，接口返回
     * @param action 1： 看题，2： 答题，3： 跳过
     * @param answer_id 问题答案，多选逗号隔开， 前两个题或者 跳过 为空
     * @param plan_page 1 计划生成页
     */
    fun obCommitAnswer(
        question_id: Int,
        action: Int,
        answer_id: String = "",
        plan_page: Int = -1
    ) {
        mRepository.obCommitAnswer(question_id, answer_id, action, plan_page)
    }

    /**
     * exp - 问卷 - 提交答案（答题）
     *
     * @param sequence 题目需要，接口返回。 前两个题：1， 2  28天计划-19 => RTQ
     * @param question_id 题目ID，接口返回，前两个题： 1， 2  28天计划-19 => RTQ
     * @param action 1： 看题，2： 答题，3： 跳过
     * @param answer_id 问题答案，多选逗号隔开， 前两个题或者 跳过 为空
     * @param page_type 1 计划生成页
     */
    fun expCommitAnswer(
        sequence: Int,
        question_id: Int,
        action: Int,
        answer_id: String = "",
        page_type: Int = -1
    ) {
        mRepository.expCommitAnswer(question_id, answer_id, action)
    }

    /**
     * 问卷 - 初始化
     */
    fun initVpPage() {
        _vpCurrentPage.value = 0
    }

    /**
     * 问卷 - 下一页
     */
    fun nextVpPage() {
        _vpCurrentPage.value = _vpCurrentPage.value?.plus(1)
    }

    /**
     * 问卷 - 上一页
     */
    fun previousVpPage(position: Int = 1) {
        val tempPosition = _vpCurrentPage.value?.toInt()?.minus(position)
        if (tempPosition != null) {
            if (tempPosition <= 0) {
                _vpCurrentPage.value = 0
            } else {
                _vpCurrentPage.value = _vpCurrentPage.value?.minus(position)
            }
        }
    }

}