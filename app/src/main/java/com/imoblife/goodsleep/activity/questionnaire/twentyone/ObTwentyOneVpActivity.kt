package com.imoblife.goodsleep.activity.questionnaire.twentyone

import android.app.Activity
import android.content.Intent
import android.view.View
import androidx.appcompat.widget.AppCompatImageView
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import com.google.gson.Gson
import com.gyf.immersionbar.BarHide
import com.gyf.immersionbar.ImmersionBar
import com.hi.dhl.jprogressview.JProgressView
import com.imoblife.goodsleep.R
import com.imoblife.goodsleep.activity.questionnaire.ObArgs
import com.imoblife.goodsleep.activity.questionnaire.ObQuestionnaireViewModel
import com.imoblife.goodsleep.bean.RTQEntity
import com.imoblife.goodsleep.constant.ConsCommon
import com.imoblife.goodsleep.databinding.LayoutAcObTwentyOneVpBinding
import com.imoblife.goodsleep.ext.onDebounceClickListener
import com.imoblife.goodsleep.ext.slideInFromLeft
import com.imoblife.goodsleep.mvvm.BaseVMActivity
import com.imoblife.goodsleep.util.AssetsUtil
import com.imoblife.goodsleep.util.ImageLoader
import com.imoblife.goodsleep.util.PaperCache
import com.imoblife.goodsleep.util.optimizefgplusvp.BlankPlaceHolderFragment
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2025/7/24
 * 描   述：ob - 5.0.3 - 问卷 - 使用FragmentTransaction实现 - 问卷详情页
 */
class ObTwentyOneVpActivity : BaseVMActivity<ObQuestionnaireViewModel>() {

    companion object {

        private const val FRAGMENT_TAG_PREFIX = "ob_fragment_"

        // 需要隐藏返回按钮的页面类型
        private val HIDE_BACK_BUTTON_TYPES = setOf(
            0, 1
        )

        // 需要隐藏进度条的页面类型
        private val HIDE_PROGRESS_BAR_TYPES = setOf(
            0, 1,
        )

        fun startActivity(context: Activity) {
            Intent(context, ObTwentyOneVpActivity::class.java).run {
                context.startActivity(this)
                context.slideInFromLeft()
            }
        }

    }

    private lateinit var mBind: LayoutAcObTwentyOneVpBinding
    private var mCurrentFm = 0
    private lateinit var mEntity: RTQEntity

    // 用于标记Fragment的标签集合
    private val fragmentTags = mutableSetOf<String>()

    override fun initImmersionBar() {
        ImmersionBar
            .with(this)
            .transparentStatusBar()
            .hideBar(BarHide.FLAG_HIDE_NAVIGATION_BAR)
            .statusBarDarkFont(false)
            .init()
    }

    override fun getLayoutResId() = R.layout.layout_ac_ob_twenty_one_vp

    override fun superInit(intent: Intent?) {}

    override fun initVM() = ViewModelProvider(this)[ObQuestionnaireViewModel::class.java]

    override fun initView() {
        mBind = mBinding as LayoutAcObTwentyOneVpBinding
        // 设置返回按钮监听
        mBind.imgBack.onDebounceClickListener { backPage() }
    }

    override fun initData() {
        lifecycleScope.launch(Dispatchers.Main) {
            // 加载问卷数据
            mEntity = getLocalRtqEntity()

            // 初始化UI
            mBind.imgBack.visibility = View.VISIBLE

            // 初始化ViewModel
            mViewModel.initVpPage()
        }
    }

    override fun startObserve() {
        mViewModel.vpCurrentPage.observe(this) { newPosition ->
            // 检查是否已完成所有问题
            if (newPosition >= mEntity.question_list.size) {
                navigateToNextActivity()
                return@observe
            }

            val forward = newPosition >= mCurrentFm
            mCurrentFm = newPosition

            // 加载新的Fragment
            loadFragment(mCurrentFm, forward)
            updateTxtUI()
        }
    }

    /**
     * 跳转到计划生成
     */
    private fun navigateToNextActivity() {
        // todo：156 跳转到计划生成
//        finish()
    }

    /**
     * 加载Fragment
     *
     * @param position Fragment位置
     * @param forward 是否前进方向(用于决定动画方向)
     */
    private fun loadFragment(position: Int, forward: Boolean) {
        val fragmentTag = getFragmentTag(position)
        val transaction = supportFragmentManager.beginTransaction()

        // 设置动画
        if (forward) {
            transaction.setCustomAnimations(
                R.anim.ob_slide_in_right,
                R.anim.ob_slide_out_left
            )
        } else {
            transaction.setCustomAnimations(
                R.anim.ob_slide_in_left,
                R.anim.ob_slide_out_right
            )
        }

        // 查找已存在的Fragment或创建新Fragment
        var fragment = supportFragmentManager.findFragmentByTag(fragmentTag)

        if (fragment == null) {
            fragment = createFragment(position)
            transaction.add(R.id.fragmentContainer, fragment, fragmentTag)
            fragmentTags.add(fragmentTag)
        }

        // 显示当前Fragment，隐藏其他Fragment
        for (tag in fragmentTags) {
            supportFragmentManager.findFragmentByTag(tag)?.let {
                if (tag == fragmentTag) {
                    transaction.show(it)
                } else {
                    transaction.hide(it)
                }
            }
        }

        transaction.commitAllowingStateLoss()

        // 更新UI状态
        updateFragmentUI(position)
    }

    /**
     * 获取Fragment标签
     */
    private fun getFragmentTag(position: Int) = "$FRAGMENT_TAG_PREFIX$position"

    /**
     * 创建Fragment
     */
    private fun createFragment(position: Int): Fragment {
        val type = mEntity.question_list[position].type
        val entity = mEntity.question_list[position]

        return when (type) {
            // ob - 问卷 - 问卷详情页 - 欢迎来到～熊猫睡眠你的睡眠健康管家 - fragment
            0 -> ObCustomizedExclusiveSleepPlanFragment.newInstance(entity)
            // ob - 问卷 - 问卷详情页 - 阶段状态页面 - fragment
            1 -> ObPagStageFragment.newInstance(entity)
            // ob - 问卷 - 问卷详情页 - 单选 - item - 「img + content」 - fragment
            2 -> ObSingleChoiceImgAndContentFragment.newInstance(entity)
            // ob - 问卷 - 问卷详情页 - 单选 - item - Grid - Span - 2 - img - fragment
            3 -> ObSingleChoiceGridImgFragment.newInstance(entity)
            // 默认显示空白Fragment
            else -> BlankPlaceHolderFragment.newInstance()
        }
    }

    /**
     * 更新Fragment UI显示
     */
    private fun updateFragmentUI(position: Int) {
        val type = mEntity.question_list[position].type

        // 返回按钮可见性
        mBind.imgBack.visibility =
            if (type in HIDE_BACK_BUTTON_TYPES) View.INVISIBLE else View.VISIBLE
    }

    /**
     * 更新页面文本UI
     */
    private fun updateTxtUI() {
        val entity = mEntity.question_list[mCurrentFm]

        mBind.apply {
            // 更新标题
            tvContent.text = entity.step_title

            // 更新进度条可见性
            groupJpvProgressStage.visibility =
                if (entity.type in HIDE_PROGRESS_BAR_TYPES) View.INVISIBLE else View.VISIBLE

            // 更新进度条
            setJpvProgressRate(entity)
        }
    }

    /**
     * 获取RtqEntity - 从本地json串gson解析
     */
    private suspend fun getLocalRtqEntity(): RTQEntity = withContext(Dispatchers.IO) {
        val (cacheKey, jsonFileName) = Pair(ConsCommon.OB_TWENTY_ONE, "question_1.5.6.json")

        // 优先从缓存获取，没有则从assets加载
        PaperCache.read<RTQEntity>(cacheKey) ?: Gson().fromJson(
            AssetsUtil.getStr(this@ObTwentyOneVpActivity, jsonFileName),
            RTQEntity::class.java
        )
    }

    /**
     * 设置四个进度条 - 进度
     */
    private fun LayoutAcObTwentyOneVpBinding.setJpvProgressRate(entity: RTQEntity.QuestionListEntity) {
        when (entity.rate_step) {
            1 -> {
                img.setCompleted()
                jpvProgress.setCurrentProgress(entity.rate)
                if (entity.rate.toInt() == 100) {
                    jpvProgressOne.setCurrentProgress()
                    jpvProgressTwo.setCurrentProgress()
                    jpvProgressThree.setCurrentProgress()
                    img.setCompleted(true)
                    imgOne.setCompleted()
                    imgTwo.setCompleted()
                    imgThree.setCompleted()
                }
            }

            2 -> {
                imgOne.setCompleted()
                jpvProgressOne.setCurrentProgress(entity.rate)
                if (entity.rate.toInt() == 100) {
                    jpvProgressTwo.setCurrentProgress()
                    jpvProgressThree.setCurrentProgress()
                    imgOne.setCompleted(true)
                    imgTwo.setCompleted()
                    imgThree.setCompleted()
                }
            }

            3 -> {
                imgTwo.setCompleted()
                jpvProgressTwo.setCurrentProgress(entity.rate)
                if (entity.rate.toInt() == 100) {
                    jpvProgressThree.setCurrentProgress()
                    imgTwo.setCompleted(true)
                    imgThree.setCompleted()
                }
            }

            4 -> {
                imgThree.setCompleted()
                jpvProgressThree.setCurrentProgress(entity.rate)
                if (entity.rate.toInt() == 100) {
                    imgThree.setCompleted(true)
                }
            }
        }
    }

    /**
     * 设置 img 是否完成阶段标识
     */
    private fun AppCompatImageView.setCompleted(isComplete: Boolean = false) {
        val drawable = if (isComplete) {
            R.mipmap.img_ob_21_jpv_progress_stage_select
        } else {
            R.mipmap.img_ob_21_jpv_progress_stage_no_select
        }
        ImageLoader.loadImageLocal(this@ObTwentyOneVpActivity, drawable, this)
    }

    /**
     * 设置 JProgressView 进度
     */
    private fun JProgressView.setCurrentProgress(rate: Float = 0F) {
        this
            .setProgress(rate)
            .setProgressColor(
                ContextCompat.getColor(
                    this@ObTwentyOneVpActivity,
                    R.color.color_white
                )
            )
            .setProgressColorBackground(
                ContextCompat.getColor(
                    this@ObTwentyOneVpActivity,
                    R.color.color_white_10
                )
            )
            .startAnimal()
    }

    /**
     * 处理返回按钮
     */
    override fun onBackPressed() {
        backPage()
    }

    /**
     * 返回上一页
     */
    private fun backPage() {
        if (ObArgs.mIsAnimationRunning) return
        if (mCurrentFm == 0) {
            finish()
        } else {
            mViewModel.previousVpPage(if (mEntity.question_list[mCurrentFm].isPrevious_transition_page) 2 else 1)
        }
    }

    /**
     * 资源释放
     */
    override fun onDestroy() {
        fragmentTags.clear()
        super.onDestroy()
    }

}