package com.imoblife.goodsleep.activity.questionnaire.twentyone

import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import com.imoblife.goodsleep.R
import com.imoblife.goodsleep.activity.questionnaire.ObQuestionnaireViewModel
import com.imoblife.goodsleep.bean.RTQEntity
import com.imoblife.goodsleep.databinding.LayoutFmObCustomizedExclusiveSleepPlanBinding
import com.imoblife.goodsleep.ext.animAlphaShow
import com.imoblife.goodsleep.ext.args
import com.imoblife.goodsleep.mvvm.BaseVMFragment
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2025/4/9
 * 描   述：ob - 问卷 - 问卷详情页 - 欢迎来到～熊猫睡眠你的睡眠健康管家 - fragment
 */
class ObCustomizedExclusiveSleepPlanFragment : BaseVMFragment<ObQuestionnaireViewModel>() {

    companion object {

        fun newInstance(entity: RTQEntity.QuestionListEntity) =
            ObCustomizedExclusiveSleepPlanFragment().apply {
                mEntity = entity
            }

    }

    private lateinit var mBind: LayoutFmObCustomizedExclusiveSleepPlanBinding

    private var mEntity: RTQEntity.QuestionListEntity by args()

    override fun getLayoutResId() = R.layout.layout_fm_ob_customized_exclusive_sleep_plan

    override fun initVM() =
        ViewModelProvider(requireActivity())[ObQuestionnaireViewModel::class.java]

    override fun initView() {
        mBind = mBinding as LayoutFmObCustomizedExclusiveSleepPlanBinding
    }

    override fun onFragmentFirstVisible() {
        super.onFragmentFirstVisible()
        mViewModel.obCommitAnswer(mEntity.question_id, 1)
    }

    override fun onFragmentResume() {
        super.onFragmentResume()
        mBind.apply {
            imgCenter.animAlphaShow({
                imgBottom.animAlphaShow({
                    lifecycleScope.launch {
                        delay(200)
                        mViewModel.apply {
                            obCommitAnswer(mEntity.question_id, 2)
                            nextVpPage()
                        }
                    }
                })
            })
        }
    }

    override fun initData() {}

    override fun startObserve() {}

}