package com.imoblife.goodsleep.activity.questionnaire.twentyone

import android.annotation.SuppressLint
import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.RecyclerView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.imoblife.goodsleep.R
import com.imoblife.goodsleep.bean.RTQEntity
import com.imoblife.goodsleep.databinding.LayoutItemObSingleChoiceImgAndContentBinding
import com.imoblife.goodsleep.ext.dp
import com.imoblife.goodsleep.ext.onDebounceClickListener
import com.imoblife.goodsleep.ext.setMipmapWithName

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2025/8/11
 * 描   述：ob - 问卷 - 问卷详情页 - 单选 - item - 「img + content」 - adapter
 */
class ObSingleChoiceImgAndContentAdapter(private val mClickBlock: ((entity: RTQEntity.QuestionListEntity.AnswerListEntity) -> Unit)) :
    BaseQuickAdapter<RTQEntity.QuestionListEntity.AnswerListEntity, ObSingleChoiceImgAndContentAdapter.VH>() {

    class VH(
        parent: ViewGroup,
        val mBind: LayoutItemObSingleChoiceImgAndContentBinding = LayoutItemObSingleChoiceImgAndContentBinding.inflate(
            LayoutInflater.from(parent.context), parent, false
        ),
    ) : RecyclerView.ViewHolder(mBind.root)

    override fun onCreateViewHolder(context: Context, parent: ViewGroup, viewType: Int): VH {
        return VH(parent)
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun onBindViewHolder(
        holder: VH,
        position: Int,
        item: RTQEntity.QuestionListEntity.AnswerListEntity?
    ) {
        item?.let {
            holder.mBind.apply {
                img.setMipmapWithName(it.img_name)
                tvContent.text = it.title
                if (it.isSelected) {
                    stvBg.strokeWidth = 2.dp.toFloat()
                    stvBg.strokeColor = ContextCompat.getColor(context, R.color.color_white_80)
                } else {
                    stvBg.strokeWidth = 1.dp.toFloat()
                    stvBg.strokeColor = ContextCompat.getColor(context, R.color.color_white_10)
                }

                root.onDebounceClickListener {
                    items.forEachIndexed { index, entity ->
                        entity.isSelected = position == index
                    }
                    mClickBlock.invoke(it)
                    notifyDataSetChanged()
                }
            }
        }
    }

}