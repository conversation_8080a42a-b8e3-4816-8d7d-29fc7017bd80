package com.imoblife.goodsleep.activity.welcome

import android.content.Context
import android.content.Intent
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import com.bytedance.ads.convert.BDConvert
import com.gyf.immersionbar.BarHide
import com.gyf.immersionbar.ImmersionBar
import com.imoblife.goodsleep.ActivityStackManager
import com.imoblife.goodsleep.R
import com.imoblife.goodsleep.activity.challenge.ChallengeActivity
import com.imoblife.goodsleep.activity.main.MainActivity
import com.imoblife.goodsleep.activity.questionnaire.twentyone.ObTwentyOneVpActivity
import com.imoblife.goodsleep.bean.CommonRouteEntity
import com.imoblife.goodsleep.constant.ConsSp
import com.imoblife.goodsleep.databinding.ActivityWelcomeBinding
import com.imoblife.goodsleep.initiatingtask.StartTaskData
import com.imoblife.goodsleep.model.ConfigMgr
import com.imoblife.goodsleep.mvvm.BaseVMActivity
import com.imoblife.goodsleep.push.MobPushCenter
import com.imoblife.goodsleep.util.CommonUtil
import com.imoblife.goodsleep.util.SpUtil
import com.imoblife.goodsleep.view.dialog.PrivacyPolicyDialog
import com.imoblife.goodsleep.view.dialog.PrivacyPolicyRetainDialog
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

//                            _ooOoo_
//                           o8888888o
//                           88" . "88
//                           (| -_- |)
//                            O\ = /O
//                        ____/`---'\____
//                      .   ' \\| |// `.
//                       / \\||| : |||// \
//                     / _||||| -:- |||||- \
//                       | | \\\ - /// | |
//                     | \_| ''\---/'' | |
//                      \ .-\__ `-` ___/-. /
//                   ___`. .' /--.--\ `. . __
//                ."" '< `.___\_<|>_/___.' >'"".
//               | | : `- \`.;`\ _ /`;.`/ - ` : | |
//                 \ \ `-. \_ __\ /__ _/ .-` / /
//         ======`-.____`-.___\_____/___.-`____.-'======
//                            `=---='
//
//         .............................................
//                  佛祖保佑             永无BUG
//          佛曰:
//                  写字楼里写字间，写字间里程序员；
//                  程序人员写程序，又拿程序换酒钱。
//                  酒醒只在网上坐，酒醉还来网下眠；
//                  酒醉酒醒日复日，网上网下年复年。
//                  但愿老死电脑间，不愿鞠躬老板前；
//                  奔驰宝马贵者趣，公交自行程序员。
//                  别人笑我忒疯癫，我笑自己命太贱；
//                  不见满街漂亮妹，哪个归得程序员？
class WelcomeActivity : BaseVMActivity<WelcomeViewModel>() {

    companion object {
        @JvmStatic
        fun startActivity(context: Context) {
            Intent(context, WelcomeActivity::class.java).let {
                context.startActivity(it)
            }
        }
    }

    private lateinit var mBind: ActivityWelcomeBinding
    private var isFirst = false
    private var isAgreePrivacy = false

    @Volatile
    private var isGoHome = true
    private var job: Job? = null
    private val privacyPolicyDialog by lazy(LazyThreadSafetyMode.NONE) { PrivacyPolicyDialog() }
    private val privacyPolicyRetainDialog by lazy(LazyThreadSafetyMode.NONE) { PrivacyPolicyRetainDialog() }

    // App启动快捷入口
    private var mFastEntryAction: String? = null

    override fun superInit(intent: Intent?) {
        intent?.apply {
            mFastEntryAction = action
            MobPushCenter.notificationClickAck(this)
            extras?.let { bundle -> MobPushCenter.clickMobPushNotify(bundle) }
        }
    }

    override fun initVM() = ViewModelProvider(this).get(WelcomeViewModel::class.java)

    override fun getLayoutResId() = R.layout.activity_welcome

    override fun initView() {
        mBind = mBinding as ActivityWelcomeBinding
        isFirst = SpUtil.getInstance().getBoolenValue(ConsSp.SP_KEY_WIZARD_FLAG, true)
        ConfigMgr.getInstance().isFirstStatApp = isFirst
        isAgreePrivacy =
            SpUtil.getInstance().getBoolenValue(ConsSp.SP_KEY_AGREE_PRIVACY_POLICY, false)
        if (isAgreePrivacy) {
            initWelcome()
        } else {
            lifecycleScope.launch {
                delay(500)
                showPrivacyPolicyDialog()
            }
        }
    }

    // 隐私弹框处、二次挽留的弹窗
    private fun showTwice() {
        privacyPolicyRetainDialog.showDialog(this, {
            ActivityStackManager.getInstance().finishAllActivity()
        }, {
            showPrivacyPolicyDialog()
        })
    }

    /**
     * 显示隐私政策弹窗
     */
    private fun showPrivacyPolicyDialog() {
        privacyPolicyDialog.showDialog(this, { showTwice() }, {
            lifecycleScope.launch {
                delay(500)
                initWelcome()
            }
        })
    }

    override fun initImmersionBar() {
        ImmersionBar.with(this).transparentBar().hideBar(BarHide.FLAG_HIDE_NAVIGATION_BAR).init()
    }

    private fun initWelcome() {
        if (isFirst) {
            SpUtil.getInstance().saveBooleanToSp(ConsSp.SP_KEY_WIZARD_FLAG, false)
        }
        StartTaskData().startFromAppOnConsentPrivacyPolicyCategoryByDsl()
        // 巨量引擎转换Sdk
        BDConvert.init(this.applicationContext, this)
        mViewModel.getAppConfig()
        job = lifecycleScope.launch {
            delay(10000)
            gotoMain()
        }
    }

    override fun startObserve() {
        mViewModel.apply {
            configInfo.observe(this@WelcomeActivity) {
                if (isFirst) {
                    gotoMain()
                } else {
                    if (it.isSuccess) {
                        it.successData?.let { _ ->
                            gotoMain()
                        } ?: run { gotoMain() }
                    } else gotoMain()
                }
            }
        }
    }

    private fun clearTimer() {
        job?.cancel()
    }

    private fun gotoMain() {
        if (isGoHome) {
            isGoHome = false
//            // 华为渠道 && 华为审核模式
//            if (ConfigMgr.getInstance().config.isAuditing_mode_huawei) {
//                MainActivity.openMainActivity(this, 0)
//            } else {
//                startObProcess(isFirst) {
//                    MainActivity.openMainActivity(this, 0)
//                    fastEntry()
//                }
//            }

            // todo:默认 - 21
            val config = ConfigMgr.getInstance().config
            config.question_type = 21
            ConfigMgr.getInstance().saveConfig(config)
            ObTwentyOneVpActivity.startActivity(this)

            finish()
        }
    }

    // App启动快捷入口
    private fun fastEntry() {
        when (mFastEntryAction ?: "") {
            // 首页
            "com.yunyang.shortcut.MAIN" -> MainActivity.openMainActivity(this, 0)
            // Qa
            "com.yunyang.shortcut.QA" -> CommonUtil.goNextBannerOrWebView(
                this,
                CommonRouteEntity(CommonUtil.TAG_APP_FAQ, "", "")
            )
            // 挑战赛
            "com.yunyang.shortcut.CHALLENGE" -> ChallengeActivity.startActivity(this, 1)
            else -> {}
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        clearTimer()
    }

    override fun onRestart() {
        super.onRestart()
        isGoHome = true
        if (isAgreePrivacy) {
            gotoMain()
        }
    }

    override fun onBackPressed() {}

    override fun initData() {}

}


