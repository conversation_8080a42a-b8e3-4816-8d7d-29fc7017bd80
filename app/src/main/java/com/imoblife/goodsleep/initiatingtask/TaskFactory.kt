package com.imoblife.goodsleep.initiatingtask

import android.content.Context
import com.didichuxing.doraemonkit.DoKit
import com.effective.android.anchors.task.Task
import com.effective.android.anchors.task.TaskCreator
import com.effective.android.anchors.task.project.Project
import com.google.gson.Gson
import com.imoblife.goodsleep.BuildConfig
import com.imoblife.goodsleep.MyApplication
import com.imoblife.goodsleep.adapter.loading.EmptyAdapter
import com.imoblife.goodsleep.adapter.loading.ErrorAdapter
import com.imoblife.goodsleep.adapter.loading.LoadingAdapter
import com.imoblife.goodsleep.adapter.loading.LoadingNightAdapter
import com.imoblife.goodsleep.appContext
import com.imoblife.goodsleep.bean.ExpEntity
import com.imoblife.goodsleep.bean.RTQEntity
import com.imoblife.goodsleep.constant.ConsCommon
import com.imoblife.goodsleep.constant.ConsConfig
import com.imoblife.goodsleep.dokit.DoKitCheckNetAndChannel
import com.imoblife.goodsleep.dokit.DoKitCheckOb
import com.imoblife.goodsleep.dokit.DoKitGetDeviceInfo
import com.imoblife.goodsleep.dokit.eventstatistics.DoKitEventStatistics
import com.imoblife.goodsleep.model.UserActionLogMgr
import com.imoblife.goodsleep.model.UserMgr
import com.imoblife.goodsleep.player.PlayCenter
import com.imoblife.goodsleep.share.ShareCenter
import com.imoblife.goodsleep.statistics.SensorsDataCenter
import com.imoblife.goodsleep.util.AssetsDBUtil
import com.imoblife.goodsleep.util.AssetsUtil
import com.imoblife.goodsleep.util.ChannelUtils
import com.imoblife.goodsleep.util.DarkModeUtils
import com.imoblife.goodsleep.util.DeviceUtil
import com.imoblife.goodsleep.util.FlashCenter
import com.imoblife.goodsleep.util.LoadingHelper
import com.imoblife.goodsleep.util.PaperCache
import com.imoblife.goodsleep.util.RomUtils
import com.imoblife.goodsleep.util.ViewType
import com.imoblife.goodsleep.util.XLog
import com.imoblife.goodsleep.view.dialog.GoodCommentDialog
import com.kongzue.dialogx.DialogX
import com.kongzue.dialogx.style.MaterialStyle
import com.mob.MobSDK
import com.scwang.smart.refresh.footer.ClassicsFooter
import com.scwang.smart.refresh.header.MaterialHeader
import com.scwang.smart.refresh.layout.SmartRefreshLayout
import com.scwang.smart.refresh.layout.api.RefreshLayout
import com.shuyu.gsyvideoplayer.cache.CacheFactory
import com.shuyu.gsyvideoplayer.cache.ProxyCacheManager
import com.shuyu.gsyvideoplayer.player.PlayerFactory
import com.shuyu.gsyvideoplayer.player.SystemPlayerManager
import com.shuyu.gsyvideoplayer.utils.GSYVideoType
import com.tencent.bugly.crashreport.CrashReport
import com.tencent.bugly.crashreport.CrashReport.UserStrategy
import com.tencent.mmkv.MMKV
import com.zzhoujay.richtext.RichText
import io.paperdb.Paper
import io.reactivex.plugins.RxJavaPlugins
import java.util.concurrent.TimeoutException

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2021-11-10
 * 描   述：启动任务_TaskFactory
 */

/**
 * MMKV
 */
class TaskInitMMKV : Task(StartTaskData.INIT_MMKV) {

    override fun run(name: String) {
        MMKV.initialize(appContext)
    }

}

/**
 * Application中 子线程 - 分组
 */
class TaskInitConfigApplicationThread : Task(StartTaskData.INIT_CONFIG_APPLICATION_THREAD, true) {

    override fun run(name: String) {
        // RichText
        RichText.initCacheDir(appContext)
        // XLog
        XLog.initLogger()
        // RxJava_Error_Handler
        RxJavaPlugins.setErrorHandler { throwable: Throwable? ->
            XLog.i(
                "RxJava catch global exception", throwable
            )
        }
        // 空祖_v3_Dialog
        //开启调试模式，在部分情况下会使用 Log 输出日志信息
        DialogX.init(MyApplication.getInstance())
        DialogX.DEBUGMODE = ConsConfig.DEBUG
        DialogX.globalStyle = MaterialStyle.style()
        //设置亮色/暗色（在启动下一个对话框时生效）
        DialogX.globalTheme = DialogX.THEME.LIGHT
        //设置 InputDialog 自动弹出键盘
        DialogX.autoShowInputKeyboard = true
        //限制 PopTip 一次只显示一个实例（关闭后可以同时弹出多个 PopTip）
        DialogX.onlyOnePopTip = true
        DialogX.cancelable = false
        // SmartRefreshLayout
        // 设置全局的Header构建器
        SmartRefreshLayout.setDefaultRefreshHeaderCreator { context: Context?, _: RefreshLayout? ->
            MaterialHeader(
                context
            )
        }
        // 设置全局的Footer构建器
        SmartRefreshLayout.setDefaultRefreshFooterCreator { context: Context?, _: RefreshLayout? ->
            ClassicsFooter(
                context
            ).setDrawableSize(20f)
        }

        // LoadingHelper_加载状态 + Toolbar
        LoadingHelper.setDefaultAdapterPool {
            register(ViewType.LOADING, LoadingAdapter())
            register(ViewType.LOADING_NIGHT, LoadingNightAdapter())
            register(ViewType.ERROR, ErrorAdapter())
            register(ViewType.EMPTY, EmptyAdapter())
        }
    }

}

/**
 * Application中 主线程 - 分组
 */
class TaskInitConfigApplicationMainThread :
    Task(StartTaskData.INIT_CONFIG_APPLICATION_MAIN_THREAD) {

    override fun run(name: String) {
        // Java Crash 捕获 - Java.util.concurrent.TimeoutException
        val defaultUncaughtExceptionHandler = Thread.getDefaultUncaughtExceptionHandler()
        Thread.setDefaultUncaughtExceptionHandler { t, e ->
            if (t.name == "FinalizerWatchdogDaemon" && e is TimeoutException) {
                // ignore it
                /*
                这种方案在 FinalizerWatchdogDaemon 出现 TimeoutException 时主动忽略这个异常，
            阻断 UncaughtExceptionHandler 链式调用，使系统默认的 UncaughtExceptionHandler 不会被调用，
            APP 就不会停止运行而继续存活下去。由于这个过程用户无感知，对用户无明显影响，可以最大限度的减少对用户的影响。
     优点 =>
        1.对系统侵入性小，不中断 FinalizerWatchdogDaemon 的运行。
        2.Thread.setDefaultUncaughtExceptionHandler() 方法是公开方法，兼容性比较好，可以适配目前所有 Android 版本。
                 */
            } else {
                defaultUncaughtExceptionHandler?.uncaughtException(t, e)
            }
        }
        // 暗黑模式
        DarkModeUtils.init(appContext)
        // MMKV
        MMKV.initialize(appContext)
        // StarrySky_配置全局的音乐播放器
        PlayCenter.getInstance().initPlayer(appContext)
        // ***** gsyVideoPlayer_配置全局 - start *****
        // 切换内核
        PlayerFactory.setPlayManager(SystemPlayerManager::class.java)
        // 代理缓存模式，支持所有模式，不支持m3u8等，默认
        CacheFactory.setCacheManager(ProxyCacheManager::class.java)
        // 切换渲染模式
        GSYVideoType.setShowType(GSYVideoType.SCREEN_TYPE_FULL)
        // ***** gsyVideoPlayer_配置全局 - end *****

        // dokitx
        if (BuildConfig.DEBUG) {
            DoKit.Builder(appContext)
//                .productId("需要使用平台功能的话，需要到dokit.cn平台申请id")
                .customKits(
                    listOf(
                        DoKitCheckNetAndChannel(),
                        DoKitGetDeviceInfo(),
                        DoKitEventStatistics(),
                        DoKitCheckOb(),
                    )
                )
                .disableUpload()
                .build()
        }
    }

}

/**
 * 同意隐私后 子线程 - 分组
 */
class TaskInitConfigWelcomeThread : Task(StartTaskData.INIT_CONFIG_WELCOME_THREAD, true) {

    override fun run(name: String) {
        // 闪验
        FlashCenter.getInstance().oneKeyLoginFlashInit()

        // MobSDK 回传用户隐私授权结果
        MobSDK.submitPolicyGrantResult(true)
        // MobSDK 分享授权
        ShareCenter.submitPolicyGrantResult()

        // Paper 非关系型数据库
        Paper.init(appContext)

        // Bugly
        if (UserMgr.getInstance().isLogin) {
            CrashReport.setUserId(appContext, UserMgr.getInstance().user.id.toString() + "")
            CrashReport.setUserSceneTag(appContext, UserMgr.getInstance().user.id)
        }
        CrashReport.setIsDevelopmentDevice(appContext, ConsConfig.DEBUG)
        val strategy = UserStrategy(appContext)
        strategy.setDeviceID(DeviceUtil.getAndroidId())
        strategy.setDeviceModel(RomUtils.getPhoneModel())
        CrashReport.initCrashReport(
            appContext,
            ConsCommon.BUGLEY_RELEASE_APP_ID,
            ConsConfig.DEBUG,
            strategy
        )
        CrashReport.setAppChannel(appContext, ChannelUtils.getChannel())

        // todo: mock data => 应用配置参数
        // 应用配置参数
//        ConfigMgr.getInstance().getAppConfigInfo()
        // todo: mock data => OPPO投放记录参数
        // OPPO投放记录参数
//        ConfigMgr.getInstance().uploadConfigInfo()

        // ob问卷 - 沉默用户
        PaperCache.write(
            ConsCommon.OB_QUESTIONNAIRE_DETAIL_STAGE_SILENT_USER, Gson().fromJson(
                AssetsUtil.getStr(appContext, "question_a_1.0.0.json"), ExpEntity::class.java
            )
        )

        // ob问卷 - 5 - tip - 提示
        PaperCache.write(
            ConsCommon.QUESTIONNAIRE_QUESTION_TYPE_NOW_STAGE, Gson().fromJson(
                AssetsUtil.getStr(appContext, "question_1.1.5.json"), ExpEntity::class.java
            )
        )

        // ob问卷 - 6 - hint - 提示
        PaperCache.write(
            ConsCommon.QUESTIONNAIRE_QUESTION_TYPE_NOW_STAGE_HINT, Gson().fromJson(
                AssetsUtil.getStr(appContext, "question_1.1.7.json"), ExpEntity::class.java
            )
        )

        // ob问卷 - 8 - transition - 过渡
        PaperCache.write(
            ConsCommon.QUESTIONNAIRE_QUESTION_TYPE_NOW_STAGE_TRANSITION, Gson().fromJson(
                AssetsUtil.getStr(appContext, "question_1.2.4.json"), ExpEntity::class.java
            )
        )

        // ob问卷 - 9 - 沉默用户
        PaperCache.write(
            ConsCommon.QUESTIONNAIRE_STAGE_SILENT_USERS, Gson().fromJson(
                AssetsUtil.getStr(appContext, "question_1.2.6.json"), ExpEntity::class.java
            )
        )

        // ob问卷 - 10 - transition - 过渡 - 在8基础上调整
        PaperCache.write(
            ConsCommon.QUESTIONNAIRE_QUESTION_TYPE_NOW_STAGE_TRANSITION_TEN, Gson().fromJson(
                AssetsUtil.getStr(appContext, "question_1.2.8_10.json"), ExpEntity::class.java
            )
        )

        // ob问卷 - 11 - transition - 过渡 - 在10基础上调整
        PaperCache.write(
            ConsCommon.QUESTIONNAIRE_QUESTION_TYPE_NOW_STAGE_TRANSITION_ELEVEN, Gson().fromJson(
                AssetsUtil.getStr(appContext, "question_1.2.8_11.json"), ExpEntity::class.java
            )
        )

        // ob问卷 - 12 - transition - 过渡 - 在10基础上调整
        PaperCache.write(
            ConsCommon.QUESTIONNAIRE_QUESTION_TYPE_NOW_STAGE_TRANSITION_TWELVE, Gson().fromJson(
                AssetsUtil.getStr(appContext, "question_1.3.0.json"), ExpEntity::class.java
            )
        )

        // ob问卷 - 13 - hint - 提示 - 在6基础上调整
        PaperCache.write(
            ConsCommon.QUESTIONNAIRE_QUESTION_TYPE_NOW_STAGE_TRANSITION_THIRTEEN, Gson().fromJson(
                AssetsUtil.getStr(appContext, "question_new_1.1.7.json"), ExpEntity::class.java
            )
        )

        // ob问卷 - 15 - 在12基础上调整
        PaperCache.write(
            ConsCommon.QUESTIONNAIRE_QUESTION_TYPE_NOW_STAGE_FIFTEEN, Gson().fromJson(
                AssetsUtil.getStr(appContext, "question_1.3.4.json"), ExpEntity::class.java
            )
        )

        // ob - 18 - json
        PaperCache.write(
            ConsCommon.OB_SIXTEEN_JSON, Gson().fromJson(
                AssetsUtil.getStr(appContext, "question_1.4.1.json"), ExpEntity::class.java
            )
        )

        // ob - 18 - json - 精力分析
        PaperCache.write(
            ConsCommon.OB_SIXTEEN_JSON_ENERGY_ANALYSIS, Gson().fromJson(
                AssetsUtil.getStr(appContext, "question_1.4.1.1.json"), ExpEntity::class.java
            )
        )

        // ob - 21 - json
        PaperCache.write(
            ConsCommon.OB_TWENTY_ONE, Gson().fromJson(
                AssetsUtil.getStr(appContext, "question_1.5.6.json"), RTQEntity::class.java
            )
        )

        // 上传使用记录(网络)
        UserActionLogMgr.getInstance().upLoadListenFailLogs()
        UserActionLogMgr.getInstance().upLoadSleepFailLogs()

        // 分享图片创建
        AssetsDBUtil.copyShareImage()
    }

}

/**
 * 同意隐私后 主线程 - 分组
 */
class TaskInitConfigWelcomeMainThread : Task(StartTaskData.INIT_CONFIG_WELCOME_MAIN_THREAD) {

    override fun run(name: String) {
        // 神策
        SensorsDataCenter.init()
        // 好评弹窗 - 状态
        GoodCommentDialog.reset(appContext)
    }

}

/**
 * 初始化SDK - 分组
 *
 * Application中分子/主线程
 * 同意隐私政策中分子/主线程
 */
object TaskCreator : TaskCreator {

    override fun createTask(taskName: String): Task {
        when (taskName) {
            StartTaskData.INIT_CONFIG_APPLICATION_MAIN_THREAD -> return TaskInitConfigApplicationMainThread()
            StartTaskData.INIT_CONFIG_APPLICATION_THREAD -> return TaskInitConfigApplicationThread()
            StartTaskData.INIT_CONFIG_WELCOME_MAIN_THREAD -> return TaskInitConfigWelcomeMainThread()
            StartTaskData.INIT_CONFIG_WELCOME_THREAD -> return TaskInitConfigWelcomeThread()
        }
        return TaskInitMMKV()
    }

}

class StartTaskFactory : Project.TaskFactory(TaskCreator)