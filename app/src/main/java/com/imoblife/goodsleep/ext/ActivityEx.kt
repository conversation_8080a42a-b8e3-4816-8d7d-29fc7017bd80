package com.imoblife.goodsleep.ext

import android.app.Activity
import com.imoblife.goodsleep.R
import com.imoblife.goodsleep.activity.member.VipSkuAActivity
import com.imoblife.goodsleep.activity.member.VipSkuBActivity
import com.imoblife.goodsleep.activity.member.VipSkuGActivity
import com.imoblife.goodsleep.activity.questionnaire.exp.EXPQuestionnaireSilentUserActivity
import com.imoblife.goodsleep.activity.questionnaire.ob.ObFifteenGuideActivity
import com.imoblife.goodsleep.activity.questionnaire.silent.SilentUsersGuideActivity
import com.imoblife.goodsleep.activity.questionnaire.sixteen.ObSixteenGuideActivity
import com.imoblife.goodsleep.activity.questionnaire.tip.TipQuestionGuideActivity
import com.imoblife.goodsleep.activity.questionnaire.transition.TransitionQuestionGuideActivity
import com.imoblife.goodsleep.activity.questionnaire.transition.TransitionQuestionnaireGuideActivity
import com.imoblife.goodsleep.activity.questionnaire.twentyone.ObTwentyOneVpActivity
import com.imoblife.goodsleep.bean.AdResourceBean
import com.imoblife.goodsleep.model.ConfigMgr
import com.imoblife.goodsleep.statistics.SensorsDataEvent
import com.imoblife.goodsleep.util.NetworkUtils

fun Activity.startVipSkuActivity(
    adResourceBean: AdResourceBean? = null,
    // 为弹出升级Vip弹窗
    isUpgradeVip: Boolean = false,
    // 来源课程
    sourceCourse: Boolean = false,
    courseId: Int = -1,
    // 是否为二次全屏
    isSecondFull: Boolean = false
) {
    adResourceBean?.let {
        if (sourceCourse) {
            SensorsDataEvent.courseBusinessLineVipPageShow()
        } else {
            SensorsDataEvent.businessLineVipPageShow(it.template_id_429, adResourceBean.id)
        }
        VipSkuGActivity.startActivity(
            this,
            adResourceBean,
            isUpgradeVip,
            sourceCourse,
            courseId,
            isSecondFull
        )
    }
}

/**
 * 启动Vip Sku模板化
 *
 * @receiver Activity
 * @param template_id 模板id
 * @param fromVipType Sku - 模板 - OB订阅页接口参数，如果点击的是svip课程加参数fromVipType=1
 * @param courseId 课程Id
 */
fun Activity.startVipSkuTemplatedActivity(
    template_id: Int = -1, fromVipType: Int = 0, courseId: Int = -1
) {
    // 是否需要挽留
    val boolRetention: Boolean
    val templateId: Int
    if (template_id == -1) {
        templateId = ConfigMgr.getInstance().config.template_id_429
        boolRetention = true
        SensorsDataEvent.questionOverVipPageShow(templateId, 0)
    } else {
        templateId = template_id
        boolRetention = false
        SensorsDataEvent.courseQuestionOverVipPageShow()
    }
    if (templateId == 3) {
        VipSkuBActivity.startActivity(this, templateId, boolRetention, fromVipType, courseId)
    } else {
        VipSkuAActivity.startActivity(this, templateId, fromVipType, courseId)
    }
}

/**
 * 启动ob流程 - 问卷
 *
 * @receiver Activity
 */
fun Activity.startObProcess(isFirstStart: Boolean, mBlockAction: (() -> Unit)? = null) {
    val config = ConfigMgr.getInstance().config
    if (config.question_type == 0) {
        if (isFirstStart && !NetworkUtils.isNetworkAvailable()) {
            SensorsDataEvent.appStartQuestionPage(
                isStartQuestion = true,
                isAppInstallStart = true,
                questionType = config.question_type
            )
            setLastQuestionPage()
        } else {
            mBlockAction?.invoke()
            SensorsDataEvent.appStartQuestionPage(
                isStartQuestion = false,
                isAppInstallStart = false,
                questionType = config.question_type
            )
        }
    } else {
        SensorsDataEvent.appStartQuestionPage(
            isStartQuestion = true,
            isAppInstallStart = isFirstStart,
            questionType = config.question_type
        )
        // 是否做问卷和问卷类型，0 不做问卷，其他根据type做哪一套问卷
        when (config.question_type) {
            // ob问卷 - 沉默用户
            1 -> EXPQuestionnaireSilentUserActivity.startActivity(this)
            // 5 - ob问卷 - tip - 提示
//            5 -> TipQuestionGuideActivity.startActivity(this)
            // 6 - ob问卷 - hint - 提示
//            6 -> HintQuestionGuideActivity.startActivity(this)
            /*
                5 & 6 => ob问卷 - 提示 - 引导
                7 => 基于6，调整计划生成页 - 「https://www.tapd.cn/tapd_fe/51322357/story/detail/1151322357001004097」
                13 => ob问卷 - 提示 - 引导 - 在6基础上调整「调整bg」
             */
            5, 6, 7, 13 -> TipQuestionGuideActivity.startActivity(this)
            /*
                8 => ob问卷 - transition - 引导
                10 => ob问卷 - transition - 引导 - 在8基础上调整
                12 => ob问卷 - transition - 引导 - 在10基础上调整
             */
            8, 10, 12 -> TransitionQuestionnaireGuideActivity.startActivity(this)
            // 9 => ob问卷 - 沉默用户 - 引导
            9 -> SilentUsersGuideActivity.startActivity(this)
            // 11 => ob问卷 - transition - 引导 - 在10基础上调整
            11 -> TransitionQuestionGuideActivity.startActivity(this)
            // 15 => 在12基础上调整
            15 -> ObFifteenGuideActivity.startActivity(this)
            // 18 => 在6基础上调整
            18 -> ObSixteenGuideActivity.startActivity(this)
            // 21 => 全新 - 【OB问卷优化】https://www.tapd.cn/tapd_fe/51322357/story/detail/1151322357001005831
            21 -> ObTwentyOneVpActivity.startActivity(this)
            // 默认 - 15 => 在12基础上调整
            else -> setLastQuestionPage()
        }
    }
}

private fun Activity.setLastQuestionPage() {
    // 默认 - 21 => 全新
    val config = ConfigMgr.getInstance().config
    config.question_type = 21
    ConfigMgr.getInstance().saveConfig(config)
    ObTwentyOneVpActivity.startActivity(this)
}

// ------------------------------  强制ob 动画 - start -------------------------------------------------------

/*
    示例用法 =>

    // 进入动画
    startActivity(Intent(this, NextActivity::class.java))
    slideInFromLeft()

    // 退出动画
    finish()
    slideOutToRight()

 */

// 进入动画: 从右到左进入
fun Activity.slideInFromLeft() {
    overridePendingTransition(R.anim.ob_slide_in_from_right, R.anim.ob_slide_out_to_left)
}

// 退出动画: 从右到左退出
fun Activity.slideOutToRight() {
    overridePendingTransition(R.anim.ob_slide_in_from_right, R.anim.ob_slide_out_to_left)
}

// ------------------------------  强制ob 动画 - end -------------------------------------------------------