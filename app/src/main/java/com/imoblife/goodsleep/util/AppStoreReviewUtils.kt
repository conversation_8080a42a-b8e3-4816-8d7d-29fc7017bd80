package com.imoblife.goodsleep.util

import android.app.Activity
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.widget.Toast
import com.imoblife.goodsleep.ActivityStackManager
import com.imoblife.goodsleep.view.dialog.DialogUtils

/**
 * 版    权：纳沃科技@版权所有
 * 创 建 者：Yunyang
 * 创建日期：2024/2/22
 * 描   述：应用商店评论
 * vivo
 * oppo
 */
object AppStoreReviewUtils {

    // --------------------------------------- oppo start ---------------------------------------
    // Q之后的软件商店包名
    private val OPPO_PKG_MK_HEYTAP = "com.heytap.market"

    // Q之前的软件商店包名
    private val OPPO_PKG_MK_OPPO = "com.oppo.market"
    private val OPPO_COMMENT_DEEPLINK_PREFIX = "oaps://mk/developer/comment?pkg="

    // 支持评论功能的软件商店版本
    private val OPPO_SUPPORT_MK_VERSION = 84000
    // --------------------------------------- oppo end ---------------------------------------

    // --------------------------------------- vivo start ---------------------------------------
    // 软件商店包名
    private val VIVO_PKG_MK = "com.bbk.appstore"

    // market://details?id=${pkg}&th_name=need_comment
    private val VIVO_COMMENT_DEEPLINK_PREFIX = "market://details?id="
    private val VIVO_COMMENT_DEEPLINK_AFTER_PREFIX = "&th_name=need_comment"

    // 支持评论功能的软件商店版本
    private val VIVO_SUPPORT_MK_VERSION = 5020
    // --------------------------------------- vivo end ---------------------------------------

    /**
     * 跳转应用市场 - 评论
     *
     * vivo、oppo依据官方文档操作，其他市场路由跳转
     */
    @JvmStatic
    fun goAppStoreReview() {
        val context = ActivityStackManager.getInstance().currentActivity
        DialogUtil.showDialog(context, "温馨提示",
            "即将为您跳转至应用市场，请确认是否继续？",
            "继续",
            { _, _ ->
                when {
                    // oppo || vivo 渠道
                    ChannelUtils.isOppoChannel() || ChannelUtils.isVivoChannel() -> {
                        val bool = jumpToComment(context)
                        if (!bool) goAppStore(context)
                    }
                    // huawei 渠道
                    ChannelUtils.isHuaWeiChannel() -> DialogUtils.showHuaWeiGoodCommentGuideDialog {
                        goAppStore(context)
                    }

                    else -> goAppStore(context)
                }
                false
            },
            "取消",
            { _, _ ->
                false
            })
    }

    /**
     * 跳转应用市场
     *
     * @param context Activity
     */
    private fun goAppStore(context: Activity) {
        try {
            val uri = Uri.parse("market://details?id=" + context.packageName)
            val intent = Intent(Intent.ACTION_VIEW, uri)
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            context.startActivity(intent)
        } catch (e: Exception) {
            Toast.makeText(context, "您的手机没有安装Android应用市场", Toast.LENGTH_SHORT).show()
            e.printStackTrace()
        }
    }

    /**
     * Oppo ｜ vivo - 拉起评论页面
     */
    private fun jumpToComment(context: Activity) = when {
        ChannelUtils.isOppoChannel() -> {
            val url = OPPO_COMMENT_DEEPLINK_PREFIX + context.packageName
            // 优先判断heytap包
            if (getVersionCode(context, OPPO_PKG_MK_HEYTAP) >= OPPO_SUPPORT_MK_VERSION) {
                jumpApp(context, Uri.parse(url), OPPO_PKG_MK_HEYTAP)
            } else if (getVersionCode(context, OPPO_PKG_MK_OPPO) >= OPPO_SUPPORT_MK_VERSION) {
                jumpApp(context, Uri.parse(url), OPPO_PKG_MK_OPPO)
            } else false
        }

        ChannelUtils.isVivoChannel() -> {
            val url =
                "$VIVO_COMMENT_DEEPLINK_PREFIX${context.packageName}$VIVO_COMMENT_DEEPLINK_AFTER_PREFIX"
            if (getVersionCode(context, VIVO_PKG_MK) >= VIVO_SUPPORT_MK_VERSION) {
                jumpApp(context, Uri.parse(url), VIVO_PKG_MK)
            } else false
        }

        else -> false
    }

    /**
     * 获取目标app版本号~
     *
     * @param context
     * @param packageName
     * @return 返回版本号
     */
    private fun getVersionCode(context: Activity, packageName: String): Long {
        var versionCode: Long = -1
        try {
            val info =
                context.packageManager.getPackageInfo(packageName, PackageManager.GET_META_DATA)
            if (info != null) {
                versionCode =
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) info.longVersionCode else info.versionCode.toLong()
            }
        } catch (e: PackageManager.NameNotFoundException) {
        }
        return versionCode
    }

    /**
     * 跳转市场 评论
     *
     * @param context Activity
     * @param uri Uri
     * @param targetPkgName String
     * @return Boolean
     */
    private fun jumpApp(context: Activity, uri: Uri, targetPkgName: String): Boolean {
        if (ChannelUtils.isOppoChannel()) {
            try {
                val intent = Intent()
                intent.setAction(Intent.ACTION_VIEW)
                intent.addCategory(Intent.CATEGORY_DEFAULT)
                intent.setPackage(targetPkgName)
                intent.setData(uri)
                // 建议采用startActivityForResult 方法启动商店页面，requestCode由调用方自定义且必须大于0，软件商店不关注
                context.startActivityForResult(intent, 100)
                return true
            } catch (e: java.lang.Exception) {
                e.printStackTrace()
            }
        } else if (ChannelUtils.isVivoChannel()) {
            try {
                val uri =
                    Uri.parse("$VIVO_COMMENT_DEEPLINK_PREFIX${context.packageName}$VIVO_COMMENT_DEEPLINK_AFTER_PREFIX")
                val intent = Intent(Intent.ACTION_VIEW, uri)
                intent.setPackage(VIVO_PKG_MK)
                context.startActivity(intent)
                return true
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
        return false
    }

}