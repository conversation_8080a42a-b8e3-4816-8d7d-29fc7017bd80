package com.imoblife.goodsleep.view.dialog

import android.annotation.SuppressLint
import android.graphics.Color
import android.text.InputFilter
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.widget.EditText
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.core.widget.doOnTextChanged
import androidx.recyclerview.widget.RecyclerView
import com.drake.softinput.hideSoftInput
import com.drake.softinput.showSoftInput
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.imoblife.goodsleep.ActivityStackManager
import com.imoblife.goodsleep.MyApplication.Companion.mWhiteNoiseCollectionState
import com.imoblife.goodsleep.MyApplication.Companion.mWhiteNoiseIsPlayingState
import com.imoblife.goodsleep.MyApplication.Companion.mWhiteNoiseTimerDuration
import com.imoblife.goodsleep.MyApplication.Companion.mWhiteNoiseTimerProgress
import com.imoblife.goodsleep.MyApplication.Companion.mWhiteNoiseTimerTime
import com.imoblife.goodsleep.R
import com.imoblife.goodsleep.activity.main.MainActivity
import com.imoblife.goodsleep.activity.member.SubscribeActivity
import com.imoblife.goodsleep.activity.monitor.SleepReminderActivity
import com.imoblife.goodsleep.activity.monitor.sleep.SleepMonitorCourseCustomTimeAdapter
import com.imoblife.goodsleep.activity.mooddiary.MoodDiaryIconAdapter
import com.imoblife.goodsleep.adapter.PlayWhiteNoiseCustomTimeAdapter
import com.imoblife.goodsleep.adapter.PlayWhiteNoiseItemAdapter
import com.imoblife.goodsleep.adapter.TimerAdapter
import com.imoblife.goodsleep.adapter.TimerAdapter.AdapterOnItemClick
import com.imoblife.goodsleep.adapter.decoration.CommonItemDecoration
import com.imoblife.goodsleep.adapter.decoration.GridSpaceItemDecoration
import com.imoblife.goodsleep.bean.AdResourceBean
import com.imoblife.goodsleep.bean.CommonCustomTimeEntity
import com.imoblife.goodsleep.bean.CommonRouteEntity
import com.imoblife.goodsleep.bean.CommonTotalEntity
import com.imoblife.goodsleep.bean.Course
import com.imoblife.goodsleep.bean.MoodDiaryIconEntity
import com.imoblife.goodsleep.constant.ConsCommon
import com.imoblife.goodsleep.constant.ConsEventCode
import com.imoblife.goodsleep.constant.ConsSp
import com.imoblife.goodsleep.databinding.LayoutDialogApneaBinding
import com.imoblife.goodsleep.databinding.LayoutDialogAudioCloudStorageBinding
import com.imoblife.goodsleep.databinding.LayoutDialogBedtimeChecklistAddBinding
import com.imoblife.goodsleep.databinding.LayoutDialogBedtimeChecklistCompleteBinding
import com.imoblife.goodsleep.databinding.LayoutDialogBedtimeChecklistDeleteBinding
import com.imoblife.goodsleep.databinding.LayoutDialogBottomMoodDiaryIconBinding
import com.imoblife.goodsleep.databinding.LayoutDialogBreathEndConclusionBinding
import com.imoblife.goodsleep.databinding.LayoutDialogBreathingExerciseCompleteBinding
import com.imoblife.goodsleep.databinding.LayoutDialogBreathingExercisePracticeNoticeBinding
import com.imoblife.goodsleep.databinding.LayoutDialogDeleteThisRecordingBinding
import com.imoblife.goodsleep.databinding.LayoutDialogFeelToWakeUpBinding
import com.imoblife.goodsleep.databinding.LayoutDialogGenerateSleepReportBinding
import com.imoblife.goodsleep.databinding.LayoutDialogGetUpTimeBinding
import com.imoblife.goodsleep.databinding.LayoutDialogGoodCommentGuideHuaweiBinding
import com.imoblife.goodsleep.databinding.LayoutDialogHeartRateUsingHelpBinding
import com.imoblife.goodsleep.databinding.LayoutDialogHomeVipPlanTipBinding
import com.imoblife.goodsleep.databinding.LayoutDialogMockDataSleepReportTipsBinding
import com.imoblife.goodsleep.databinding.LayoutDialogMoodDiaryDeleteBinding
import com.imoblife.goodsleep.databinding.LayoutDialogMoodDiarySaveStateBinding
import com.imoblife.goodsleep.databinding.LayoutDialogRadarChartTipsBinding
import com.imoblife.goodsleep.databinding.LayoutDialogRedemptionCodeTipBinding
import com.imoblife.goodsleep.databinding.LayoutDialogSleepDecibelBinding
import com.imoblife.goodsleep.databinding.LayoutDialogSleepMonitoringTipBinding
import com.imoblife.goodsleep.databinding.LayoutDialogSleepProcessDeleteBinding
import com.imoblife.goodsleep.databinding.LayoutDialogSleepReportTipsBinding
import com.imoblife.goodsleep.databinding.LayoutDialogSleepSoundRecordingBinding
import com.imoblife.goodsleep.databinding.LayoutDialogUnexpectedInterruptionBinding
import com.imoblife.goodsleep.databinding.LayoutDialogVipPlanSetReminderBinding
import com.imoblife.goodsleep.databinding.LayoutDialogVipPlanSetReminderNotificationBinding
import com.imoblife.goodsleep.databinding.LayoutDialogWhiteNoiseCollectBinding
import com.imoblife.goodsleep.databinding.LayoutDialogWhiteNoiseCutomTimeBinding
import com.imoblife.goodsleep.databinding.LayoutViewBottomPlayWhiteNoiseBinding
import com.imoblife.goodsleep.databinding.LayoutViewSleepMonitorSetTimingBinding
import com.imoblife.goodsleep.databinding.LayoutViewWhiteNoiseSetTimingBinding
import com.imoblife.goodsleep.event.BaseEvent
import com.imoblife.goodsleep.ext.dp
import com.imoblife.goodsleep.ext.onDebounceClickListener
import com.imoblife.goodsleep.model.UserMgr
import com.imoblife.goodsleep.player.PlayCenter
import com.imoblife.goodsleep.statistics.SensorsDataEvent
import com.imoblife.goodsleep.util.CacheWhiteNoiseUtil
import com.imoblife.goodsleep.util.CommonUtil
import com.imoblife.goodsleep.util.DateUtil
import com.imoblife.goodsleep.util.EmptyUtils
import com.imoblife.goodsleep.util.ImageLoader
import com.imoblife.goodsleep.util.SpUtil
import com.imoblife.goodsleep.util.StringUtil
import com.imoblife.goodsleep.util.TimeUtils
import com.imoblife.goodsleep.util.ToastUtils
import com.imoblife.goodsleep.util.XLog
import com.jaychang.st.SimpleText
import com.kongzue.dialogx.dialogs.BottomDialog
import com.kongzue.dialogx.dialogs.CustomDialog
import com.kongzue.dialogx.interfaces.BottomDialogSlideEventLifecycleCallback
import com.kongzue.dialogx.interfaces.OnBindView
import org.greenrobot.eventbus.EventBus


object DialogUtils {

    private val context by lazy { ActivityStackManager.getInstance().currentActivity }

    fun playTimerDialog() {
        CustomDialog.build()
            .setCustomView(object : OnBindView<CustomDialog>(R.layout.activity_timer) {
                override fun onBind(dialog: CustomDialog, v: View) {
                    val timerAdapter = TimerAdapter()
                    val timerRecycler = v.findViewById<RecyclerView>(R.id.timer_recycler)
                    timerRecycler.adapter = timerAdapter
                    timerAdapter.setAdapterOnItemClick(object : AdapterOnItemClick {
                        override fun onClick(position: Int, data: Any?) {
                            when (val currentTime = data as Int) {
                                -3 -> {
                                    //播放完当前音频
                                    val duration = PlayCenter.getInstance().duration
                                    val current = PlayCenter.getInstance().position
                                    val times = duration - current
                                    PlayCenter.getInstance().playerControl.stopByTimedOff(
                                        times, isPause = false, isFinishCurrSong = true
                                    )
                                    dialog.dismiss()
                                }

                                -2 -> {
                                    //取消
                                    dialog.dismiss()
                                }

                                -1 -> {
                                    //自定义
                                    val bottomSheetDialog = BottomSheetDialog(
                                        customView.context, R.style.BottomSheetEdit
                                    )
                                    val view: View = LayoutInflater.from(customView.context)
                                        .inflate(R.layout.layout_input_timer, null)
                                    bottomSheetDialog.setContentView(view)
                                    bottomSheetDialog.show()
                                    val editText =
                                        view.findViewById<EditText>(R.id.timer_input_edit)
                                    editText.filters = arrayOf<InputFilter>(
                                        InputFilter.LengthFilter(4)
                                    ) //限制输入4位
                                    val timerStart = view.findViewById<TextView>(R.id.timer_start)
                                    timerStart.setOnClickListener {
                                        val timer = editText.text.toString().trim { it <= ' ' }
                                        if (TextUtils.isEmpty(timer)) {
                                            ToastUtils.showLongToastCenter("自定义时间不能为空哦")
                                            return@setOnClickListener
                                        }
                                        if (timer.length > 5) {
                                            ToastUtils.showLongToastCenter("自定义时间不能超过24小时哦")
                                        }
                                        try {
                                            val timerSecond = Integer.valueOf(timer).toLong()
                                            if (timerSecond > 1400) {
                                                ToastUtils.showLongToastCenter("自定义时间不能超过24小时哦")
                                                return@setOnClickListener
                                            }
                                            PlayCenter.getInstance().playerControl.stopByTimedOff(
                                                timerSecond * 60 * 1000,
                                                isPause = false,
                                                isFinishCurrSong = false
                                            )
                                            if (bottomSheetDialog != null && bottomSheetDialog.isShowing) {
                                                bottomSheetDialog.dismiss()
                                            }
                                        } catch (e: Exception) {
                                            ToastUtils.showLongToastCenter("自定义时间不能超过24小时哦")
                                        }
                                        dialog.dismiss()
                                    }
                                }

                                else -> {
                                    XLog.e("tag", "========currentTime===$currentTime")
                                    PlayCenter.getInstance().playerControl.stopByTimedOff(
                                        (currentTime * 1000).toLong(),
                                        isPause = false,
                                        isFinishCurrSong = false
                                    )
                                    dialog.dismiss()
                                }
                            }
                        }
                    })
                }
            }).setAlign(CustomDialog.ALIGN.BOTTOM).setAnimResId(R.anim.bottom_in, R.anim.bottom_out)
            .show()
    }

    /**
     * Dialog - 睡眠分贝
     */
    @Deprecated("废弃,可使用 - showSleepReportTipsDialog(3)")
    fun showIdealSleepingEnvironmentDialog() {
        CustomDialog.build()
            .setCustomView(object : OnBindView<CustomDialog>(R.layout.layout_dialog_sleep_decibel) {
                override fun onBind(dialog: CustomDialog?, v: View?) {
                    v?.let {
                        LayoutDialogSleepDecibelBinding.bind(it).apply {
                            stvBtn.onDebounceClickListener { dialog?.dismiss() }
                            tvContent.text =
                                SimpleText.from(context.getString(R.string.string_ideal_sleeping_environment_txt))
                                    .first(context.getString(R.string.string_ideal_decibel_txt))
                                    .textColor(R.color.color_FF4600)
                        }
                    }
                }
            }).setMaskColor(ContextCompat.getColor(context, R.color.black40)).show()
    }

    /**
     * Dialog - 呼吸暂停 | 呼吸障碍
     */
    @Deprecated("废弃,可使用 - showSleepReportTipsDialog(8)")
    fun showApneaDialog() {
        CustomDialog.build()
            .setCustomView(object : OnBindView<CustomDialog>(R.layout.layout_dialog_apnea) {
                override fun onBind(dialog: CustomDialog?, v: View?) {
                    v?.let {
                        LayoutDialogApneaBinding.bind(it).apply {
                            stvBtn.onDebounceClickListener { dialog?.dismiss() }
                            tvContent.text =
                                SimpleText.from("睡觉时呼吸暂停10秒及以上可能存在呼吸暂停风险。\n每小时呼吸720-1200次为正常。\n呼吸障碍＜5次为低，5-14次为轻，\n15-30次为中，＞30次为重。")
                                    .first("10秒").textColor(R.color.color_FF4600).first("720-1200")
                                    .textColor(R.color.color_FF4600).first("＜5")
                                    .textColor(R.color.color_FF4600).first("5-14")
                                    .textColor(R.color.color_FF4600).first("15-30")
                                    .textColor(R.color.color_FF4600).first("＞30")
                                    .textColor(R.color.color_FF4600)
                        }
                    }
                }
            }).setMaskColor(ContextCompat.getColor(context, R.color.black40)).show()
    }

    /**
     * Dialog - 删除该录音
     */
    fun showDeleteThisRecordingDialog(blockAction: (() -> Unit)) {
        CustomDialog.build().setCustomView(object :
            OnBindView<CustomDialog>(R.layout.layout_dialog_delete_this_recording) {
            override fun onBind(dialog: CustomDialog?, v: View?) {
                v?.let {
                    LayoutDialogDeleteThisRecordingBinding.bind(it).apply {
                        stvBtnNo.onDebounceClickListener { dialog?.dismiss() }
                        stvBtnYes.onDebounceClickListener {
                            blockAction.invoke()
                            dialog?.dismiss()
                        }
                    }
                }
            }
        }).setMaskColor(ContextCompat.getColor(context, R.color.black40)).show()
    }

    /**
     * Dialog - 生成睡眠报告
     */
    fun showGenerateSleepReportDialog() {
        CustomDialog.build().setCustomView(object :
            OnBindView<CustomDialog>(R.layout.layout_dialog_generate_sleep_report) {
            override fun onBind(dialog: CustomDialog?, v: View?) {
                v?.let {
                    LayoutDialogGenerateSleepReportBinding.bind(it).apply {
                        stvBtn.onDebounceClickListener { dialog?.dismiss() }
                    }
                }
            }
        }).setMaskColor(ContextCompat.getColor(context, R.color.black40)).show()
    }

    /**
     * Dialog - 睡眠报告 - 意外中断
     */
    fun showUnexpectedInterruptionDialog() {
        CustomDialog.build().setCustomView(object :
            OnBindView<CustomDialog>(R.layout.layout_dialog_unexpected_interruption) {
            override fun onBind(dialog: CustomDialog?, v: View?) {
                v?.let {
                    LayoutDialogUnexpectedInterruptionBinding.bind(it).apply {
                        stvBtn.onDebounceClickListener { dialog?.dismiss() }
                    }
                }
            }
        }).setMaskColor(ContextCompat.getColor(context, R.color.black40)).show()
    }

    /**
     * 睡眠报告 - 雷达图Tips
     *
     * @param uiMonthStyle 是否为月UI
     */
    @Deprecated("废弃,可使用 - showSleepReportTipsDialog(6|7)")
    fun showRadarChartTipsDialog(uiMonthStyle: Boolean = false) {
        CustomDialog.build().setCustomView(object :
            OnBindView<CustomDialog>(R.layout.layout_dialog_radar_chart_tips) {
            override fun onBind(dialog: CustomDialog?, v: View?) {
                v?.let {
                    LayoutDialogRadarChartTipsBinding.bind(it).apply {
                        tvContent.text = if (uiMonthStyle) {
                            context.getString(R.string.string_radar_chart_tips_month_txt)
                        } else {
                            context.getString(R.string.string_radar_chart_tips_week_txt)
                        }
                        stvBtn.onDebounceClickListener { dialog?.dismiss() }
                    }
                }
            }
        }).setMaskColor(ContextCompat.getColor(context, R.color.black40)).show()
    }

    /**
     * Dialog - 首页会员计划 - 重要提示
     */
    fun showHomeVipPlanTipDialog(restart: (() -> Unit)) {
        CustomDialog.build().setCustomView(object :
            OnBindView<CustomDialog>(R.layout.layout_dialog_home_vip_plan_tip) {
            override fun onBind(dialog: CustomDialog?, v: View?) {
                v?.let {
                    LayoutDialogHomeVipPlanTipBinding.bind(it).apply {
                        tvContentBottom.onDebounceClickListener { dialog?.dismiss() }
                        stvBtn.onDebounceClickListener {
                            restart.invoke()
                            dialog?.dismiss()
                        }
                    }
                }
            }
        }).setMaskColor(ContextCompat.getColor(context, R.color.black40)).show()
    }

    /**
     * Dialog - 睡眠报告 - Tips
     *
     * @param position 位置 =>
     * 0 - 睡眠得分；
     * 1 - 睡眠情况；「
     * 睡眠情况 - 2024/11/24 调整为 睡眠周期
     * 睡眠周期 - 2025/4/7 调整为 睡眠阶段
     * 」
     * 2 - 睡眠声音记录；
     * 3 - 睡眠环境；
     * 4 - 睡眠建议；
     * 5 - 为什么没有声音？；
     * 6 - 雷达图中的数据为本月平均数值；
     * 7 - 雷达图中的数据为本周平均数值；
     * 8 - 呼吸障碍；
     * 9 - 专属推荐；
     * 10 - 睡眠健康；
     * 11 - 播放记录；
     * 12 - 睡眠分析；
     * 13 - 睡眠阶段 - 效率
     * 14 - 睡眠分析 - 深睡
     * 15 - 睡眠分析 - 浅睡
     * 16 - 睡眠分析 - 清醒
     * 17 - 睡眠分析 - 入睡时长
     * 18 - 睡眠分析 - 睡眠效率
     */
    fun showSleepReportTipsDialog(position: Int) {
        CustomDialog.build().setCustomView(object :
            OnBindView<CustomDialog>(R.layout.layout_dialog_sleep_report_tips) {
            @SuppressLint("SetTextI18n")
            override fun onBind(dialog: CustomDialog?, v: View?) {
                v?.let {
                    LayoutDialogSleepReportTipsBinding.bind(it).apply {
                        tvTitle.visibility = View.VISIBLE
                        when (position) {
                            // 睡眠得分
                            0 -> {
                                tvTitle.text = "睡眠得分是如何得出的？"
                                tvContent.text =
                                    "熊猫睡眠通过手机自带陀螺仪和麦克风来采集、分析睡眠过程中的环境中的环境噪音、入睡时长、深浅睡比例、梦话与呼噜声等数据，结合你的近期和长期睡眠规律，来评估你的睡眠得分。"
                            }
                            // 睡眠情况
                            // 睡眠情况 - 2024/11/24 调整为 睡眠周期
                            // 睡眠周期 - 2025/4/7 调整为 睡眠阶段
                            1 -> {
                                tvTitle.text = "睡眠阶段"
                                tvContent.text =
                                    "简单地讲，一个成年人的睡眠周期约为90-100分钟，是非快速眼动睡眠（包含浅睡、深睡）和快速眼动睡眠交替进行的过程。浅睡阶段，体温下降、心率放缓、逐渐入眠。深睡阶段，肌肉放松，血压下降，呼吸更有规律，大脑对外界反应不敏感，帮助修复肌肉、生长身体、巩固记忆。快速眼动阶段，身体仍在深睡，但大脑已经清醒。熊猫睡眠会根据你本次睡眠的浅睡、深睡、梦/醒比例作出分析和建议。"
                            }
                            // 睡眠声音记录
                            2 -> {
                                tvTitle.text = "为什么要记录呼噜声和梦话？"
                                tvContent.text =
                                    "打呼噜是一种常见的睡眠紊乱现象，会导致血氧浓度下降，减少深度睡眠。记录呼噜声，可更好地了解自己的身体状况。\n小贴士：监测声音需要打开麦克风权限。这些声音只会保存在你的设备中，不用担心其他人。"
                            }
                            // 睡眠环境
                            3 -> {
                                tvTitle.text = "睡眠环境中的噪音"
                                tvContent.text =
                                    "为了保证睡眠质量，夜间环境噪音不宜超过40分贝。长期处于超过50分贝，将显著影响休息与睡眠质量，持续暴露可能导致注意力分散与疲劳感加重。"
                            }
                            // 睡眠建议
                            4 -> {
                                tvTitle.text = "睡眠建议"
                                tvContent.text =
                                    "基于专利的AI智能监测算法，深度识别睡眠周期，准确评估你的睡眠质量并提供改善方案。记录每一次进步，让你读懂自己的睡眠。\n小贴士：长期连续使用睡眠监测，AI将会更懂你的睡眠行为，为你提供更精准的睡眠建议。"
                            }
                            // 为什么没有声音？
                            5 -> {
                                tvTitle.text = "为什么没有声音？"
                                tvContent.text =
                                    "1.麦克风权限被其他应用占用了 2.麦克风正常但手机离你较远 3.你昨晚睡的很好，真的没有发出声音。"
                            }
                            // 雷达图中的数据为本月平均数值
                            6 -> {
                                tvTitle.visibility = View.GONE
                                tvContent.text = "雷达图中的数据为本月平均数值。"
                            }
                            // 雷达图中的数据为本周平均数值
                            7 -> {
                                tvTitle.visibility = View.GONE
                                tvContent.text = "雷达图中的数据为本周平均数值。"
                            }
                            // 呼吸障碍
                            8 -> {
                                tvTitle.text = "什么是呼吸障碍？"
                                tvContent.text =
                                    "睡觉时呼吸暂停10S及以上可能存在呼吸暂停风险。每分钟呼吸12-20次为正常。"
                            }
                            // 专属推荐
                            9 -> {
                                tvTitle.text = "专属内容推荐"
                                tvContent.text =
                                    "根据你的睡眠质量、睡眠症状，为你量身推荐内容或功能，助你好眠。"
                            }
                            // 睡眠健康
                            10 -> {
                                tvTitle.text = "睡眠与形象的关系"
                                tvContent.text =
                                    "充足的睡眠能有效促进身体新城代谢改善皮肤、毛发和个人精气神状态。虽然此处的黑眼圈、脱发及肤质虽老风险值并非科学分析，仅供娱乐参考，但希望你能重视睡眠质量，让它为你的颜值保驾护航。"
                            }
                            // 播放记录
                            11 -> {
                                tvTitle.text = "播放记录"
                                tvContent.text =
                                    "这里展示你在睡前听过的氛围音和助眠内容，帮助你记忆这些内容对入睡和睡眠质量的影响效果。坚持睡前收听会对改善睡眠有非常大帮助。"
                            }
                            // 睡眠分析
                            12 -> {
                                tvTitle.text = "睡眠阶段"
                                tvContent.text =
                                    "一个成年人的睡眠周期约为90-100分钟，是非快速眼动睡眠（包含浅睡、深睡）和快速眼动（包含轻度睡眠）交替进行的过程。熊猫睡眠会根据你本次的睡眠的浅睡、深睡、轻度睡眠比例做出分析和建议。"
                            }
                            // 睡眠阶段 - 效率
                            13 -> {
                                tvTitle.text = "睡眠效率"
                                tvContent.text =
                                    "是指有效睡眠时长与在床时长之比，体现了在床期间醒与睡眠的比例，通常使用这个指标来判断存在睡眠障碍风险的大小，正常的睡眠效率在85%以上。建议：睡觉只上床，床上只睡觉、困了再上床、超过20分钟睡不着就离床。"
                            }
                            // 14 - 睡眠分析 - 深睡
                            14 -> {
                                tvTitle.text = "深睡"
                                tvContent.text =
                                    "深睡是睡眠周期中的一种状态，此时身体肌肉放松、血压下降，呼吸更有规律，大脑对外界反应不敏感，帮助修复肌肉、身体生长、巩固记忆。深睡通常占睡眠周期的15%-25%（1-2小时）。"
                            }
                            // 15 - 睡眠分析 - 浅睡
                            15 -> {
                                tvTitle.text = "浅睡"
                                tvContent.text =
                                    "浅睡是睡眠周期中的一种状态，此时体温下降，逐渐入眠。浅睡通常占睡眠周期的45%-60%（3.5-5.4小时）。"
                            }
                            // 16 - 睡眠分析 - 清醒
                            16 -> {
                                tvTitle.text = "轻度睡眠"
                                tvContent.text =
                                    "轻度睡眠是睡眠周期中的一种状态，身体仍在熟睡，但大脑已经清醒。"
                            }
                            // 17 - 睡眠分析 - 入睡时长
                            17 -> {
                                tvTitle.text = "入睡时长"
                                tvContent.text =
                                    "从你开始睡眠监测到你已经入睡的时长。入睡用时不超过30分钟视为正常，否则可认为入睡困难。"
                            }
                            // 18 - 睡眠分析 - 睡眠效率
                            18 -> {
                                tvTitle.text = "睡眠效率"
                                tvContent.text =
                                    "是指有效睡眠时长与睡眠时长之比，通过使用这个指标来判断存在睡眠障碍风险的大小，正常的睡眠效率在85%以上。建议：睡觉只上床，床上只睡觉、困了再上床、超过20分钟睡不着就离床。"
                            }
                            // 为什么没有声音？
                            else -> {
                                tvTitle.text = "为什么没有声音？"
                                tvContent.text =
                                    "1.麦克风权限被其他应用占用了 2.麦克风正常但手机离你较远 3.你昨晚睡的很好，真的没有发出声音。"
                            }
                        }
                        tvBtn.onDebounceClickListener { dialog?.dismiss() }
                    }
                }
            }
        }).setMaskColor(ContextCompat.getColor(context, R.color.black40)).show()
    }

    /**
     * Dialog - 睡眠报告 - Tips
     *
     * @param position 位置 =>
     * 19 - 睡眠报告 - 天 - 蒙版 - 你还没有生成睡眠报告 - 「当用户已经设置提醒弹窗」
     * 20 - 睡眠报告 - 天 - 蒙版 - 你还没有生成睡眠报告 - 「当用户未设置提醒弹窗」
     */
    fun showMockDataSleepReportTipsDialog(position: Int) {
        CustomDialog.build().setCustomView(object :
            OnBindView<CustomDialog>(R.layout.layout_dialog_mock_data_sleep_report_tips) {
            @SuppressLint("SetTextI18n")
            override fun onBind(dialog: CustomDialog?, v: View?) {
                v?.let {
                    LayoutDialogMockDataSleepReportTipsBinding.bind(it).apply {
                        tvTitle.visibility = View.VISIBLE
                        when (position) {
                            // 19 - 睡眠报告 - 天 - 蒙版 - 你还没有生成睡眠报告 - 「当用户已经设置提醒弹窗」
                            19 -> {
                                tvTitle.text = "你还没有生成睡眠报告"
                                tvContent.text = "当前为示例报告，使用睡眠监测后才能生成你的报告哦。"
                            }
                            // 20 - 睡眠报告 - 天 - 蒙版 - 你还没有生成睡眠报告 - 「当用户未设置提醒弹窗」
                            20 -> {
                                tvTitle.text = "你还没有生成睡眠报告"
                                tvContent.text =
                                    "当前为示例报告，设置提醒后，我们会提醒你使用睡眠监测功能，生成你的睡眠报告。"
                                tvBtn.text = "去设置"
                            }
                            // 为什么没有声音？
                            else -> {
                                tvTitle.text = "为什么没有声音？"
                                tvContent.text =
                                    "1.麦克风权限被其他应用占用了 2.麦克风正常但手机离你较远 3.你昨晚睡的很好，真的没有发出声音。"
                            }
                        }
                        tvBtn.onDebounceClickListener {
                            if (position == 20) {
                                SleepReminderActivity.startActivity(context)
                            }
                            dialog?.dismiss()
                        }
                    }
                }
            }
        }).setMaskColor(ContextCompat.getColor(context, R.color.black40)).show()
    }

    /**
     * Dialog - 入睡｜起床时间
     */
    fun showTimeSelectDialog(
        title: String, hour: Int, min: Int, action: ((value: String) -> Unit)
    ) {
        CustomDialog.build()
            .setCustomView(object : OnBindView<CustomDialog>(R.layout.layout_dialog_get_up_time) {
                @SuppressLint("SimpleDateFormat")
                override fun onBind(dialog: CustomDialog?, v: View?) {
                    v?.let {
                        LayoutDialogGetUpTimeBinding.bind(it).apply {
                            var mHour = hour
                            var mMinute = min
                            var timeValue = "$mHour:$mMinute"
                            numberPickerStart.value = mHour
                            numberPickerEnd.value = mMinute
                            tvTitle.text = title
                            numberPickerStart.setOnValueChangedListener { _, _, hourValue ->
                                mHour = hourValue
                                timeValue = StringUtil.formatTime(hourValue)
                                    .toString() + ":" + StringUtil.formatTime(mMinute)
                            }
                            numberPickerEnd.setOnValueChangedListener { _, _, minute ->
                                mMinute = minute
                                timeValue = StringUtil.formatTime(mHour)
                                    .toString() + ":" + StringUtil.formatTime(minute)
                            }
                            tvContentBottom.onDebounceClickListener { dialog?.dismiss() }
                            stvBtn.onDebounceClickListener {
                                if (timeValue != "") {
                                    action.invoke(timeValue)
                                }
                                dialog?.dismiss()
                            }
                        }
                    }
                }
            }).setMaskColor(ContextCompat.getColor(context, R.color.black40)).show()
    }

    /**
     * Dialog - 睡前清单 - 打卡完成
     */
    fun showBedtimeChecklistCompleteDialog(submit: (() -> Unit)) {
        CustomDialog.build().setCustomView(object :
            OnBindView<CustomDialog>(R.layout.layout_dialog_bedtime_checklist_complete) {
            override fun onBind(dialog: CustomDialog?, v: View?) {
                v?.let {
                    LayoutDialogBedtimeChecklistCompleteBinding.bind(it).apply {
                        stvBtn.onDebounceClickListener {
                            submit.invoke()
                            dialog?.dismiss()
                        }
                    }
                }
            }
        }).setMaskColor(ContextCompat.getColor(context, R.color.black40)).setCancelable(true).show()
    }

    /**
     * Dialog - 睡前清单 - 新增事件
     */
    fun showAddBedtimeChecklistDialog(actionInput: ((value: String) -> Unit)) {
        CustomDialog.build().setCustomView(object :
            OnBindView<CustomDialog>(R.layout.layout_dialog_bedtime_checklist_add) {
            @SuppressLint("SetTextI18n")
            override fun onBind(dialog: CustomDialog?, v: View?) {
                v?.let {
                    LayoutDialogBedtimeChecklistAddBinding.bind(it).apply {
                        tvWordCount.text = "0/8"
                        editContent.doOnTextChanged { text, _, _, _ ->
                            val length = text?.length ?: 0
                            tvWordCount.text = "$length/8"
                            tvPrompt.visibility = if (length == 8) View.VISIBLE else View.GONE
                        }
                        stvBtn.onDebounceClickListener {
                            val content = editContent.text.toString().trim()
                            if (TextUtils.isEmpty(content)) {
                                ToastUtils.showLongToastCenter(context.getString(R.string.string_input_event_empty_tips))
                            } else {
                                actionInput.invoke(editContent.text.toString())
                                dialog?.dismiss()
                            }
                        }
                    }
                }
            }
        }).setCancelable(true).setMaskColor(ContextCompat.getColor(context, R.color.black40)).show()
    }

    /**
     * Dialog - 睡前清单 - 删除自定义事件
     */
    fun showDeleteBedtimeChecklistDialog(actionDelete: (() -> Unit)) {
        CustomDialog.build().setCustomView(object :
            OnBindView<CustomDialog>(R.layout.layout_dialog_bedtime_checklist_delete) {
            override fun onBind(dialog: CustomDialog?, v: View?) {
                v?.let {
                    LayoutDialogBedtimeChecklistDeleteBinding.bind(it).apply {
                        stvBtnCancel.onDebounceClickListener { dialog?.dismiss() }
                        stvBtnConfirm.onDebounceClickListener {
                            actionDelete.invoke()
                            dialog?.dismiss()
                        }
                    }
                }
            }
        }).setMaskColor(ContextCompat.getColor(context, R.color.black40)).show()
    }

    /**
     * BottomDialog - 心情日记 - 标签Icon
     */
    fun showMoodDiaryIconDialog(
        list: List<MoodDiaryIconEntity>, actionSelect: ((entity: MoodDiaryIconEntity) -> Unit)
    ) {
        val dialog = BottomDialog.show(object :
            OnBindView<BottomDialog?>(R.layout.layout_dialog_bottom_mood_diary_icon) {
            override fun onBind(dialog: BottomDialog?, v: View) {
                LayoutDialogBottomMoodDiaryIconBinding.bind(v).apply {
                    val adapter = MoodDiaryIconAdapter { entity ->
                        actionSelect.invoke(entity)
                        dialog?.dismiss()
                    }
                    recyclerView.setHasFixedSize(true)
                    recyclerView.addItemDecoration(GridSpaceItemDecoration(4, 16.dp, 14.dp))
                    recyclerView.adapter = adapter
                    adapter.submitList(list)
                }
            }
        })
        dialog.isCancelable = true
        dialog.backgroundColor = Color.parseColor("#0A0E28")
        dialog.setMaskColor(ContextCompat.getColor(context, R.color.black40))
    }

    /**
     * Dialog - 心情日记 - 删除日记
     */
    fun showDeleteMoodDiaryDialog(actionDelete: (() -> Unit)) {
        CustomDialog.build().setCustomView(object :
            OnBindView<CustomDialog>(R.layout.layout_dialog_mood_diary_delete) {
            override fun onBind(dialog: CustomDialog?, v: View?) {
                v?.let {
                    LayoutDialogMoodDiaryDeleteBinding.bind(it).apply {
                        stvBtnCancel.onDebounceClickListener { dialog?.dismiss() }
                        stvBtnConfirm.onDebounceClickListener {
                            actionDelete.invoke()
                            dialog?.dismiss()
                        }
                    }
                }
            }
        }).setMaskColor(ContextCompat.getColor(context, R.color.black40)).show()
    }

    /**
     * Dialog - 心情日记 - 未保存状态
     */
    fun showMoodDiarySaveStateDialog(actionDelete: (() -> Unit)) {
        CustomDialog.build().setCustomView(object :
            OnBindView<CustomDialog>(R.layout.layout_dialog_mood_diary_save_state) {
            override fun onBind(dialog: CustomDialog?, v: View?) {
                v?.let {
                    LayoutDialogMoodDiarySaveStateBinding.bind(it).apply {
                        stvBtnCancel.onDebounceClickListener { dialog?.dismiss() }
                        stvBtnConfirm.onDebounceClickListener {
                            actionDelete.invoke()
                            dialog?.dismiss()
                        }
                    }
                }
            }
        }).setMaskColor(ContextCompat.getColor(context, R.color.black40)).show()
    }

    /**
     * Dialog - 呼吸练习 - 结论
     *
     * @param count 呼吸练习次数
     */
    @JvmStatic
    fun showBreathEndDialog(count: Int?) {
        CustomDialog.build().setCustomView(object :
            OnBindView<CustomDialog>(R.layout.layout_dialog_breath_end_conclusion) {
            override fun onBind(dialog: CustomDialog?, v: View?) {
                v?.let {
                    LayoutDialogBreathEndConclusionBinding.bind(it).apply {
                        tvTitle.text = TimeUtils.getBreathTimeFrame()
                        tvTime.text = DateUtil.getDateYMDAndWeek()
                        val minute = SpUtil.getInstance().getStringValue(
                            "${UserMgr.getInstance().loginUserId}${ConsCommon.BREATH_TIMING_DURATION}",
                            "1"
                        )
                        val mode = SpUtil.getInstance().getStringValue(
                            "${UserMgr.getInstance().loginUserId}${ConsCommon.BREATH_TIMING_MODE_NAME}",
                            "减压助眠"
                        )
                        tvExerciseDuration.text = SimpleText.from("${minute}分钟").first(minute)
                            .textColor(R.color.color_18D9EB).size(30).bold()
                        tvBreathPattern.text = mode
                        val numberOfBreaths = count ?: 0
                        tvNumberOfBreaths.text = SimpleText.from("${numberOfBreaths}次")
                            .first(numberOfBreaths.toString()).textColor(R.color.color_18D9EB)
                            .size(30).bold()
                        imgClose.onDebounceClickListener { dialog?.dismiss() }
                    }
                }
            }
        }).setMaskColor(ContextCompat.getColor(context, R.color.color_black_70)).show()
    }

    /**
     * Dialog - 睡眠历程 - 删除记录
     */
    fun showDeleteSleepProcessDialog(actionDelete: (() -> Unit)) {
        CustomDialog.build().setCustomView(object :
            OnBindView<CustomDialog>(R.layout.layout_dialog_sleep_process_delete) {
            override fun onBind(dialog: CustomDialog?, v: View?) {
                v?.let {
                    LayoutDialogSleepProcessDeleteBinding.bind(it).apply {
                        stvBtnCancel.onDebounceClickListener { dialog?.dismiss() }
                        stvBtnConfirm.onDebounceClickListener {
                            actionDelete.invoke()
                            dialog?.dismiss()
                        }
                    }
                }
            }
        }).setMaskColor(ContextCompat.getColor(context, R.color.black40)).show()
    }

    /**
     * Dialog - 如何测量 - 使用帮助
     */
    fun showHeartRateUsingHelpDialog() {
        CustomDialog.build().setCustomView(object :
            OnBindView<CustomDialog>(R.layout.layout_dialog_heart_rate_using_help) {
            override fun onBind(dialog: CustomDialog?, v: View?) {
                v?.let {
                    LayoutDialogHeartRateUsingHelpBinding.bind(it).apply {
                        imgClose.onDebounceClickListener { dialog?.dismiss() }
                        stvBtn.onDebounceClickListener { dialog?.dismiss() }
                    }
                }
            }
        }).setMaskColor(ContextCompat.getColor(context, R.color.black40)).show()
    }

    /**
     * Dialog - 「华为」引导 - 如何对我们App进行评分
     */
    fun showHuaWeiGoodCommentGuideDialog(action: (() -> Unit)) {
        CustomDialog.build().setCustomView(object :
            OnBindView<CustomDialog>(R.layout.layout_dialog_good_comment_guide_huawei) {
            override fun onBind(dialog: CustomDialog?, v: View?) {
                v?.let {
                    LayoutDialogGoodCommentGuideHuaweiBinding.bind(it).apply {
                        img.onDebounceClickListener {
                            action.invoke()
                            dialog?.dismiss()
                        }
                        imgClose.onDebounceClickListener { dialog?.dismiss() }
                    }
                }
            }
        }).setMaskColor(ContextCompat.getColor(context, R.color.black40)).show()
    }

    // Dialog - 底部弹窗 - 播放白噪音
    private var mBottomPlayWhiteNoiseDialog: BottomDialog? = null

    fun getBottomPlayWhiteNoiseDialog() = mBottomPlayWhiteNoiseDialog

    // Dialog - 底部弹窗 - 白噪音 - 设定定时
    private var mWhiteNoiseSetTimingDialog: BottomDialog? = null

    fun startWhiteNoiseCollect(it: CommonTotalEntity) {
        if (mWhiteNoiseCollectionState) {
            EventBus.getDefault().post(
                BaseEvent(
                    ConsEventCode.REFRESH_COURSE_WHITE_NOISE_COLLECTION, Pair(0, "")
                )
            )
        } else {
            showWhiteNoiseCollectDialog("混音${it.total}")
        }
    }

    /**
     * Dialog - 底部弹窗 - 播放白噪音
     */
    fun showBottomPlayWhiteNoiseDialog(actionGetTotalCount: (() -> Unit)) {
        mBottomPlayWhiteNoiseDialog = BottomDialog.build().setCustomView(object :
            OnBindView<BottomDialog?>(R.layout.layout_view_bottom_play_white_noise) {
            override fun onBind(dialog: BottomDialog?, v: View?) {
                v?.let {
                    LayoutViewBottomPlayWhiteNoiseBinding.bind(it).apply {
                        recyclerView.addItemDecoration(
                            CommonItemDecoration(
                                16.dp, 16.dp, 20.dp, 24.dp, 20.dp, 24.dp
                            )
                        )
                        val adapter = PlayWhiteNoiseItemAdapter {
                            if (EmptyUtils.isEmpty(CacheWhiteNoiseUtil.getCacheSingletonMap())) {
                                dialog?.dismiss()
                            }
                        }
                        recyclerView.adapter = adapter
                        if (EmptyUtils.isNotEmpty(CacheWhiteNoiseUtil.getCacheSingletonMap())) {
                            val list = mutableListOf<Course>()
                            CacheWhiteNoiseUtil.getCacheSingletonMap().forEach { entry ->
                                list.add(entry.value)
                            }
                            list.reverse()
                            adapter.submitList(list)
                        }
                        imgPlay.setImageDrawable(
                            ContextCompat.getDrawable(
                                context,
                                if (mWhiteNoiseIsPlayingState) R.mipmap.img_bottom_dialog_center_pause else R.mipmap.img_bottom_dialog_center_play
                            )
                        )
                        imgCollectReverb.setImageDrawable(
                            if (mWhiteNoiseCollectionState) {
                                ContextCompat.getDrawable(
                                    context, R.mipmap.img_collect_reverb_select
                                )
                            } else {
                                ContextCompat.getDrawable(
                                    context, R.mipmap.img_collect_reverb
                                )
                            }
                        )
                        val maxProgress = 30 * 60
                        jProgressView.setProgress(mWhiteNoiseTimerProgress)
                            .setMaxProgress(maxProgress).resetValue()
                        tvSetTiming.text =
                            if (!TextUtils.isEmpty(mWhiteNoiseTimerTime)) mWhiteNoiseTimerTime else "设定定时"

                        // 关闭
                        imgClose.onDebounceClickListener {
                            dialog?.dismiss()
                        }
                        // 全部清除
                        tvAllClear.onDebounceClickListener {
                            EventBus.getDefault().post(BaseEvent(ConsEventCode.WHITE_NOISE_CLOSE))
                            dialog?.dismiss()
                        }
                        // 设定定时
                        llSetTiming.onDebounceClickListener { showWhiteNoiseSetTimingDialog() }
                        // 收藏混音
                        llCollectReverb.onDebounceClickListener {
                            actionGetTotalCount.invoke()
                        }
                        // 播放｜暂停
                        imgPlay.onDebounceClickListener {
                            imgPlay.setImageDrawable(
                                if (mWhiteNoiseIsPlayingState) {
                                    ContextCompat.getDrawable(
                                        context, R.mipmap.img_bottom_dialog_center_play
                                    )
                                } else {
                                    ContextCompat.getDrawable(
                                        context, R.mipmap.img_bottom_dialog_center_pause
                                    )
                                }
                            )
                            mWhiteNoiseIsPlayingState = !mWhiteNoiseIsPlayingState
                            EventBus.getDefault()
                                .post(BaseEvent(ConsEventCode.WHITE_NOISE_PLAY_OR_PAUSE_PLAYER))
                        }
                    }
                }
            }
        }).setBackgroundColor(ContextCompat.getColor(context, R.color.color_0A1D43))
            .setCancelable(true)
            .setAllowInterceptTouch(false)
            .setMaskColor(ContextCompat.getColor(context, R.color.black40))
            .setDialogLifecycleCallback(object :
                BottomDialogSlideEventLifecycleCallback<BottomDialog>() {
                override fun onDismiss(dialog: BottomDialog?) {
                    super.onDismiss(dialog)
                    mBottomPlayWhiteNoiseDialog = null
                }
            }).show()
    }

    /**
     * Dialog - 底部弹窗 - 白噪音 - 设定定时
     */
    fun showWhiteNoiseSetTimingDialog() {
        mWhiteNoiseSetTimingDialog = BottomDialog.build().setCustomView(object :
            OnBindView<BottomDialog?>(R.layout.layout_view_white_noise_set_timing) {
            override fun onBind(dialog: BottomDialog?, v: View?) {
                v?.let {
                    LayoutViewWhiteNoiseSetTimingBinding.bind(it).apply {
                        recyclerView.addItemDecoration(
                            CommonItemDecoration(
                                10.dp, 10.dp, 20.dp, 42.dp, 20.dp, 42.dp
                            )
                        )
                        var minuteValue = 30
                        val adapter = PlayWhiteNoiseCustomTimeAdapter { position, minute ->
                            if (position == 5) {
                                showWhiteNoiseCustomTimeDialog()
                            }
                            minuteValue = minute
                        }
                        recyclerView.adapter = adapter
                        val list = mutableListOf<CommonCustomTimeEntity>()
                        context.resources.getStringArray(R.array.string_white_noise_custom_time)
                            .forEachIndexed { index, content ->
                                list.add(
                                    CommonCustomTimeEntity(
                                        content, index == 2
                                    )
                                )
                            }
                        adapter.submitList(list)
                        // 取消
                        tvCancel.onDebounceClickListener { dialog?.dismiss() }
                        // 完成
                        tvComplete.onDebounceClickListener {
                            mWhiteNoiseTimerDuration = minuteValue
                            EventBus.getDefault()
                                .post(BaseEvent(ConsEventCode.REFRESH_COURSE_WHITE_NOISE_RX_TIMER_DURATION))
                            dialog?.dismiss()
                        }
                    }
                }
            }
        }).setBackgroundColor(ContextCompat.getColor(context, R.color.color_0A1D43))
            .setMaskColor(ContextCompat.getColor(context, R.color.black40)).setCancelable(true)
            .setAllowInterceptTouch(false)
            .show()
    }

    /**
     * Dialog - 白噪音 - 混音 - 自定义时间
     */
    fun showWhiteNoiseCustomTimeDialog() {
        CustomDialog.build().setCustomView(object :
            OnBindView<CustomDialog>(R.layout.layout_dialog_white_noise_cutom_time) {
            @SuppressLint("SetTextI18n")
            override fun onBind(dialog: CustomDialog?, v: View?) {
                v?.let {
                    LayoutDialogWhiteNoiseCutomTimeBinding.bind(it).apply {
                        val defaultValue = mWhiteNoiseTimerDuration.toString()
                        editContent.setText(defaultValue)
                        editContent.setSelection(defaultValue.length)
                        editContent.postDelayed({ editContent.showSoftInput() }, 300L)
                        stvBtn.onDebounceClickListener {
                            val content = editContent.text.toString().trim()
                            if (TextUtils.isEmpty(content)) {
                                ToastUtils.showLongToastCenter("请输入定时时间")
                            } else {
                                mWhiteNoiseTimerDuration = content.toInt()
                                EventBus.getDefault()
                                    .post(BaseEvent(ConsEventCode.REFRESH_COURSE_WHITE_NOISE_RX_TIMER_DURATION))
                                editContent.hideSoftInput()
                                mWhiteNoiseSetTimingDialog?.dismiss()
                                dialog?.dismiss()
                            }
                        }
                    }
                }
            }
        }).setCancelable(true).setMaskColor(ContextCompat.getColor(context, R.color.black40)).show()
    }

    /**
     * Dialog - 白噪音 - 混音 - 收藏
     */
    fun showWhiteNoiseCollectDialog(value: String) {
        CustomDialog.build().setCustomView(object :
            OnBindView<CustomDialog>(R.layout.layout_dialog_white_noise_collect) {
            @SuppressLint("SetTextI18n")
            override fun onBind(dialog: CustomDialog?, v: View?) {
                v?.let {
                    LayoutDialogWhiteNoiseCollectBinding.bind(it).apply {
                        tvWordCount.text = "0/8"
                        editContent.setText(value)
                        editContent.doOnTextChanged { text, _, _, _ ->
                            val length = text?.length ?: 0
                            tvWordCount.text = "$length/8"
                            tvPrompt.visibility = if (length == 8) View.VISIBLE else View.GONE
                        }
                        editContent.setSelection(value.length)
                        editContent.post { editContent.showSoftInput() }
                        stvBtn.onDebounceClickListener {
                            val content = editContent.text.toString().trim()
                            if (TextUtils.isEmpty(content)) {
                                ToastUtils.showLongToastCenter("请输入混音名称")
                            } else {
                                EventBus.getDefault().post(
                                    BaseEvent(
                                        ConsEventCode.REFRESH_COURSE_WHITE_NOISE_COLLECTION,
                                        Pair(1, content)
                                    )
                                )
                                editContent.hideSoftInput()
                                dialog?.dismiss()
                            }
                        }
                    }
                }
            }
        }).setCancelable(true).setMaskColor(ContextCompat.getColor(context, R.color.black40)).show()
    }

    /**
     * Dialog - 音频云端存储 - 引导
     */
    fun showAudioCloudStorageDialog() {
        CustomDialog.build().setCustomView(object :
            OnBindView<CustomDialog>(R.layout.layout_dialog_audio_cloud_storage) {
            override fun onBind(dialog: CustomDialog?, v: View?) {
                v?.let {
                    LayoutDialogAudioCloudStorageBinding.bind(it).apply {
                        imgStart.onDebounceClickListener { dialog?.dismiss() }
                        imgEnd.onDebounceClickListener {
                            SubscribeActivity.startActivity(context)
                            dialog?.dismiss()
                        }
                    }
                }
            }
        }).setMaskColor(ContextCompat.getColor(context, R.color.black40)).show()
    }

    /**
     * Dialog - 睡眠声音记录 - 删除
     */
    fun showDeleteSleepSoundRecordingDialog(actionDelete: (() -> Unit)) {
        CustomDialog.build().setCustomView(object :
            OnBindView<CustomDialog>(R.layout.layout_dialog_sleep_sound_recording) {
            override fun onBind(dialog: CustomDialog?, v: View?) {
                v?.let {
                    LayoutDialogSleepSoundRecordingBinding.bind(it).apply {
                        stvBtnCancel.onDebounceClickListener { dialog?.dismiss() }
                        stvBtnConfirm.onDebounceClickListener {
                            actionDelete.invoke()
                            dialog?.dismiss()
                        }
                    }
                }
            }
        }).setMaskColor(ContextCompat.getColor(context, R.color.black40)).show()
    }

    /**
     * Dialog - 心率监测 - 监测提醒
     */
    fun showDeleteHeartRateRemindDialog(actionDelete: (() -> Unit)) {
        CustomDialog.build().setCustomView(object :
            OnBindView<CustomDialog>(R.layout.layout_dialog_mood_diary_delete) {
            @SuppressLint("SetTextI18n")
            override fun onBind(dialog: CustomDialog?, v: View?) {
                v?.let {
                    LayoutDialogMoodDiaryDeleteBinding.bind(it).apply {
                        tvTitle.text = "删除提醒"
                        tvSubTitle.text = "确认要删除该条提醒吗？"
                        stvBtnCancel.onDebounceClickListener { dialog?.dismiss() }
                        stvBtnConfirm.onDebounceClickListener {
                            actionDelete.invoke()
                            dialog?.dismiss()
                        }
                    }
                }
            }
        }).setMaskColor(ContextCompat.getColor(context, R.color.black40)).show()
    }

    /**
     * Dialog - 站内通知 - 删除
     */
    fun showDeleteOnSiteNotificationsDialog(actionDelete: (() -> Unit)) {
        CustomDialog.build().setCustomView(object :
            OnBindView<CustomDialog>(R.layout.layout_dialog_mood_diary_delete) {
            @SuppressLint("SetTextI18n")
            override fun onBind(dialog: CustomDialog?, v: View?) {
                v?.let {
                    LayoutDialogMoodDiaryDeleteBinding.bind(it).apply {
                        tvTitle.text = "删除消息"
                        tvSubTitle.text = "确认要删除该条消息吗？"
                        stvBtnCancel.onDebounceClickListener { dialog?.dismiss() }
                        stvBtnConfirm.onDebounceClickListener {
                            actionDelete.invoke()
                            dialog?.dismiss()
                        }
                    }
                }
            }
        }).setMaskColor(ContextCompat.getColor(context, R.color.black40)).show()
    }

    /**
     * Dialog - 底部弹窗 - 睡眠监测 - 设定定时
     */
    @JvmStatic
    fun showSleepMonitorSetTimingDialog() {
        BottomDialog
            .build()
            .setCustomView(object :
                OnBindView<BottomDialog?>(R.layout.layout_view_sleep_monitor_set_timing) {
                override fun onBind(dialog: BottomDialog?, v: View?) {
                    v?.let {
                        LayoutViewSleepMonitorSetTimingBinding.bind(it).apply {
                            recyclerView.addItemDecoration(
                                CommonItemDecoration(
                                    10.dp, 10.dp, 30.dp, 42.dp, 20.dp, 30.dp
                                )
                            )
                            // 默认选中 - 「默认」
                            var currentPosition = SpUtil.getInstance()
                                .getIntValue(
                                    ConsSp.SP_KEY_SLEEP_MONITOR_COURSE_SET_TIME_RV_SELECT,
                                    6
                                )
                            var currentMinute = SpUtil.getInstance()
                                .getIntValue(
                                    ConsSp.SP_KEY_SLEEP_MONITOR_COURSE_SET_TIME_RV_SELECT_TIME,
                                    0
                                )
                            val adapter = SleepMonitorCourseCustomTimeAdapter { position, minute ->
                                currentPosition = position
                                currentMinute = minute
                                SpUtil.getInstance().saveIntToSp(
                                    ConsSp.SP_KEY_SLEEP_MONITOR_COURSE_SET_TIME_RV_SELECT,
                                    currentPosition
                                )
                                SpUtil.getInstance().saveIntToSp(
                                    ConsSp.SP_KEY_SLEEP_MONITOR_COURSE_SET_TIME_RV_SELECT_TIME,
                                    currentMinute
                                )
                            }
                            recyclerView.adapter = adapter
                            val list = mutableListOf<CommonCustomTimeEntity>()
                            context.resources.getStringArray(R.array.string_sleep_monitor_course_custom_time)
                                .forEachIndexed { index, content ->
                                    list.add(
                                        CommonCustomTimeEntity(
                                            content, index == currentPosition
                                        )
                                    )
                                }
                            adapter.submitList(list)

                            tvComplete.onDebounceClickListener {
                                if (currentMinute == -1) {
                                    // 默认 - 关闭倒计时
                                    PlayCenter.getInstance().playerControl.stopByTimedOff(
                                        0, isPause = false, isFinishCurrSong = false
                                    )
                                } else {
                                    PlayCenter.getInstance().playerControl.stopByTimedOff(
                                        currentMinute * 60 * 1000L,
                                        isPause = false,
                                        isFinishCurrSong = false
                                    )
                                }
                                // 睡眠监测 - 设置音频倒计时后 - 启动播放
                                EventBus.getDefault()
                                    .post(BaseEvent(ConsEventCode.SLEEP_MONITOR_SET_COUNT_DOWN_PLAY_MUSIC))
                                dialog?.dismiss()
                            }
                        }
                    }
                }
            })
            .setBackgroundColor(ContextCompat.getColor(context, R.color.color_0A1D43))
            .setMaskColor(ContextCompat.getColor(context, R.color.black40))
            .setCancelable(true)
            .setAllowInterceptTouch(false)
            .show()
    }

    /**
     * Dialog - 睡眠监测 - 设定定时 - 自定义时间
     */
    fun showSleepMonitorCustomTimeDialog() {
        CustomDialog
            .build()
            .setCustomView(object :
                OnBindView<CustomDialog>(R.layout.layout_dialog_white_noise_cutom_time) {
                override fun onBind(dialog: CustomDialog?, v: View?) {
                    v?.let { view ->
                        LayoutDialogWhiteNoiseCutomTimeBinding.bind(view).apply {
                            // 限制输入4位
                            editContent.filters = arrayOf<InputFilter>(InputFilter.LengthFilter(4))
                            editContent.postDelayed({ editContent.showSoftInput() }, 300L)

                            stvBtn.setOnClickListener {
                                val timer = editContent.text.toString().trim { it <= ' ' }
                                if (TextUtils.isEmpty(timer)) {
                                    ToastUtils.showLongToastCenter("自定义时间不能为空哦")
                                    return@setOnClickListener
                                }
                                if (timer.length > 5) {
                                    ToastUtils.showLongToastCenter("自定义时间不能超过24小时哦")
                                }
                                try {
                                    val timerSecond = Integer.valueOf(timer).toLong()
                                    if (timerSecond > 1400) {
                                        ToastUtils.showLongToastCenter("自定义时间不能超过24小时哦")
                                        return@setOnClickListener
                                    }
                                    PlayCenter.getInstance().playerControl.stopByTimedOff(
                                        timerSecond * 60 * 1000,
                                        isPause = false,
                                        isFinishCurrSong = false
                                    )
                                    dialog?.dismiss()
                                } catch (e: Exception) {
                                    ToastUtils.showLongToastCenter("自定义时间不能超过24小时哦")
                                }
                            }
                        }
                    }
                }
            })
            .setCancelable(true)
            .setMaskColor(ContextCompat.getColor(context, R.color.black40))
            .show()
    }

    /**
     * Dialog - 睡前仪式 - 设定定时
     *
     * @param minute 当前分钟数
     * @param actionComplete 回调 - 分钟数
     */
    fun showSleepCeremonyDialog(minute: Int, actionComplete: ((minuteValue: Int) -> Unit)) {
        BottomDialog
            .build()
            .setCustomView(object :
                OnBindView<BottomDialog?>(R.layout.layout_view_white_noise_set_timing) {
                override fun onBind(dialog: BottomDialog?, v: View?) {
                    v?.let {
                        LayoutViewWhiteNoiseSetTimingBinding.bind(it).apply {
                            recyclerView.addItemDecoration(
                                CommonItemDecoration(
                                    10.dp, 10.dp, 20.dp, 42.dp, 20.dp, 22.dp
                                )
                            )
                            var minuteValue = 5
                            val adapter = PlayWhiteNoiseCustomTimeAdapter { _, minute ->
                                minuteValue = minute
                            }
                            recyclerView.adapter = adapter
                            val list = mutableListOf<CommonCustomTimeEntity>()
                            context.resources.getStringArray(R.array.string_sleep_ceremony_time)
                                .forEach { content ->
                                    list.add(
                                        CommonCustomTimeEntity(
                                            content, content == "${minute}分钟"
                                        )
                                    )
                                }
                            adapter.submitList(list)
                            // 取消
                            tvCancel.onDebounceClickListener { dialog?.dismiss() }
                            // 完成
                            tvComplete.onDebounceClickListener {
                                actionComplete.invoke(minuteValue)
                                dialog?.dismiss()
                            }
                        }
                    }
                }
            }).setBackgroundColor(ContextCompat.getColor(context, R.color.color_0A1D43))
            .setMaskColor(ContextCompat.getColor(context, R.color.black40)).setCancelable(true)
            .setAllowInterceptTouch(false)
            .show()
    }

    /**
     * Dialog - 睡眠报告 - 会员计划 - 设置提醒
     */
    fun showVipPlanSetReminderDialog(callBack: ((Pair<Int, Int>?) -> Unit)) {
        BottomDialog
            .build()
            .setCustomView(object :
                OnBindView<BottomDialog?>(R.layout.layout_dialog_vip_plan_set_reminder) {
                override fun onBind(dialog: BottomDialog?, v: View?) {
                    v?.let {
                        LayoutDialogVipPlanSetReminderBinding.bind(it).apply {
                            var mHour = 21
                            var mMinute = 30

                            numberPickerStart.setOnValueChangedListener { _, _, hour ->
                                mHour = hour
                            }
                            numberPickerEnd.setOnValueChangedListener { _, _, minute ->
                                mMinute = minute
                            }

                            imgClose.onDebounceClickListener {
                                SensorsDataEvent.setReminderPopupClick("关闭", "睡眠报告页")
                                callBack.invoke(null)
                                dialog?.dismiss()
                            }
                            stvBtn.onDebounceClickListener {
                                SensorsDataEvent.setReminderPopupClick("设置提醒", "睡眠报告页")
                                callBack.invoke(Pair(mHour, mMinute))
                                dialog?.dismiss()
                            }
                        }
                    }
                }
            })
            .setBackgroundColor(ContextCompat.getColor(context, R.color.color_1A1F33))
            .setMaskColor(ContextCompat.getColor(context, R.color.black40))
            .setAllowInterceptTouch(false)
            .show()
    }

    /**
     * Dialog - 睡眠报告 - 会员计划 - 设置提醒 - 开启通知
     *
     */
    fun showVipPlanSetReminderNotificationDialog(callBack: (() -> Unit)) {
        CustomDialog
            .build()
            .setCustomView(object :
                OnBindView<CustomDialog>(R.layout.layout_dialog_vip_plan_set_reminder_notification) {
                override fun onBind(dialog: CustomDialog?, v: View?) {
                    v?.let { view ->
                        LayoutDialogVipPlanSetReminderNotificationBinding.bind(view).apply {
                            stvBtnStart.onDebounceClickListener {
                                dialog?.dismiss()
                            }
                            stvBtnEnd.onDebounceClickListener {
                                callBack.invoke()
                                dialog?.dismiss()
                            }
                        }
                    }
                }
            })
            .setMaskColor(ContextCompat.getColor(context, R.color.black40))
            .show()
    }

    /**
     * Dialog - 兑换码 - Tip
     */
    fun showRedemptionCodeTipDialog(
        isSuccess: Boolean = false,
        content: String = "请输入正确兑换码",
        action: (() -> Unit)? = null
    ) {
        CustomDialog
            .build()
            .setCustomView(object :
                OnBindView<CustomDialog>(R.layout.layout_dialog_redemption_code_tip) {
                @SuppressLint("SetTextI18n")
                override fun onBind(dialog: CustomDialog?, v: View?) {
                    v?.let {
                        LayoutDialogRedemptionCodeTipBinding.bind(it).apply {
                            tvTitle.text = if (isSuccess) "兑换成功" else "兑换失败"
                            tvSubTitle.text = content

                            stvBtn.onDebounceClickListener {
                                dialog?.dismiss()
                                action?.invoke()
                            }
                        }
                    }
                }
            })
            .setMaskColor(ContextCompat.getColor(context, R.color.black40))
            .show()
    }

    /**
     * Dialog - 呼吸训练 - 练习须知
     */
    fun showBreathingExercisePracticeNoticeDialog(
        content: String,
        action: ((bool: Boolean) -> Unit)? = null
    ) {
        CustomDialog
            .build()
            .setCustomView(object :
                OnBindView<CustomDialog>(R.layout.layout_dialog_breathing_exercise_practice_notice) {
                override fun onBind(dialog: CustomDialog?, v: View?) {
                    v?.let {
                        LayoutDialogBreathingExercisePracticeNoticeBinding.bind(it).apply {
                            tvSubTitle.text = content

                            imgClose.onDebounceClickListener {
                                action?.invoke(false)
                                dialog?.dismiss()
                            }
                            stvBtn.onDebounceClickListener {
                                action?.invoke(true)
                                dialog?.dismiss()
                            }
                        }
                    }
                }
            })
            .setMaskColor(ContextCompat.getColor(context, R.color.black40))
            .show()
    }

    /**
     * Dialog - 呼吸训练 - 完成本次训练
     *
     * @param triple Triple<String, String, String> 练习时长｜练习模式｜呼吸次数
     * @param actionShare Function0<Unit>?
     */
    fun showBreathingExerciseCompleteDialog(
        triple: Triple<String, String, String>,
        actionShare: ((view: View) -> Unit)? = null
    ) {
        CustomDialog
            .build()
            .setCustomView(object :
                OnBindView<CustomDialog>(R.layout.layout_dialog_breathing_exercise_complete) {
                override fun onBind(dialog: CustomDialog?, v: View?) {
                    v?.let {
                        LayoutDialogBreathingExerciseCompleteBinding.bind(it).apply {
                            tvPracticeDuration.text = triple.first
                            tvPracticeType.text = triple.second
                            tvNumberOfBreaths.text = triple.third

                            imgClose.onDebounceClickListener {
                                dialog?.dismiss()
                            }
                            stvBtn.onDebounceClickListener {
                                dialog?.dismiss()
                                actionShare?.invoke(container)
                            }
                        }
                    }
                }
            })
            .setMaskColor(ContextCompat.getColor(context, R.color.black100))
            .show()
    }

    /**
     * Dialog - 睡眠监测 - 提示｜激励弹窗「设置睡眠提醒｜我知道了｜开始监测」
     */
    fun showSleepMonitoringTipDialog(entity: AdResourceBean, activity: MainActivity) {
        CustomDialog
            .build()
            .setCustomView(object :
                OnBindView<CustomDialog>(R.layout.layout_dialog_sleep_monitoring_tip) {
                override fun onBind(dialog: CustomDialog?, v: View?) {
                    v?.let {
                        LayoutDialogSleepMonitoringTipBinding.bind(it).apply {
                            ImageLoader.loadImageUrl(activity, entity.imgURL, img)

                            viewClose.onDebounceClickListener {
                                dialog?.dismiss()
                            }
                            viewBtn.onDebounceClickListener {
                                dialog?.dismiss()
                                CommonUtil.goNextBannerOrWebView(
                                    activity,
                                    CommonRouteEntity(
                                        entity.tag,
                                        entity.lessionsID,
                                        entity.title,
                                        entity.page_id,
                                        entity.category_id
                                    )
                                )
                            }
                        }
                    }
                }
            })
            .setMaskColor(ContextCompat.getColor(activity, R.color.color_black_70))
            .show(activity)
    }

    /**
     * Dialog - 睡眠监测 - 你醒来的感受如何？
     *
     * @param updateAction 上报行为
     */
    fun showFeelToWakeUpDialog(updateAction: ((action: Int, dialog: CustomDialog?) -> Unit)) {
        CustomDialog
            .build()
            .setCustomView(object :
                OnBindView<CustomDialog>(R.layout.layout_dialog_feel_to_wake_up) {
                override fun onBind(dialog: CustomDialog?, v: View?) {
                    v?.let {
                        LayoutDialogFeelToWakeUpBinding.bind(it).apply {
                            val selectTextColor =
                                ContextCompat.getColor(context, R.color.color_white)
                            val selectSolidColor =
                                ContextCompat.getColor(context, R.color.color_0B97FF)

                            val defaultTextColor =
                                ContextCompat.getColor(context, R.color.color_black)
                            val defaultSolidColor =
                                ContextCompat.getColor(context, R.color.color_F1F3F6)

                            stvTipOne.onDebounceClickListener {
                                stvTipOne.setTextColor(selectTextColor)
                                stvTipOne.solid = selectSolidColor

                                stvTipTwo.setTextColor(defaultTextColor)
                                stvTipTwo.solid = defaultSolidColor

                                stvTipThree.setTextColor(defaultTextColor)
                                stvTipThree.solid = defaultSolidColor

                                updateAction.invoke(1, dialog)
                            }
                            stvTipTwo.onDebounceClickListener {
                                stvTipOne.setTextColor(defaultTextColor)
                                stvTipOne.solid = defaultSolidColor

                                stvTipTwo.setTextColor(selectTextColor)
                                stvTipTwo.solid = selectSolidColor

                                stvTipThree.setTextColor(defaultTextColor)
                                stvTipThree.solid = defaultSolidColor

                                updateAction.invoke(2, dialog)
                            }
                            stvTipThree.onDebounceClickListener {
                                stvTipOne.setTextColor(defaultTextColor)
                                stvTipOne.solid = defaultSolidColor

                                stvTipTwo.setTextColor(defaultTextColor)
                                stvTipTwo.solid = defaultSolidColor

                                stvTipThree.setTextColor(selectTextColor)
                                stvTipThree.solid = selectSolidColor

                                updateAction.invoke(3, dialog)
                            }
                        }
                    }
                }
            })
            .setMaskColor(ContextCompat.getColor(context, R.color.color_black_70))
            .setCancelable(false)
            .setAlign(CustomDialog.ALIGN.BOTTOM)
            .show()
    }

}